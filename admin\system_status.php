<?php
$page_title = 'System Status';
$show_nav = true;
require_once '../includes/header.php';
require_once '../database/setup_helper.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

$helper = new DatabaseSetupHelper();
$health = $helper->getSystemHealth();
$stats = $helper->getDatabaseStats();

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'cleanup_tokens') {
        $days = $_POST['days'] ?? 30;
        $cleaned = $helper->cleanupExpiredTokens($days);
        $_SESSION['success_message'] = "Cleaned up $cleaned expired tokens.";
        redirect('system_status.php');
    } elseif ($action === 'backup_database') {
        $success = $helper->backupDatabase();
        if ($success) {
            $_SESSION['success_message'] = 'Database backup created successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to create database backup.';
        }
        redirect('system_status.php');
    }
}
?>

<style>
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.status-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.status-item:last-child {
    border-bottom: none;
}

.status-good {
    color: #28a745;
    font-weight: bold;
}

.status-bad {
    color: #dc3545;
    font-weight: bold;
}

.status-warning {
    color: #ffc107;
    font-weight: bold;
}

.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.health-good {
    background: #28a745;
}

.health-bad {
    background: #dc3545;
}

.health-warning {
    background: #ffc107;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.action-btn {
    padding: 1rem;
    text-align: center;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
}
</style>

<div style="max-width: 1200px; margin: 0 auto;">
    <div class="dashboard-header" style="text-align: center; margin-bottom: 2rem;">
        <h2>🔧 System Status</h2>
        <p style="color: #666;">Monitor your mess management system health and performance</p>
    </div>

    <!-- Overall Health Status -->
    <div class="card" style="margin-bottom: 2rem;">
        <div class="card-header">
            <h3>🏥 System Health Overview</h3>
        </div>
        <div class="card-body">
            <?php if (empty($health['issues'])): ?>
                <div style="text-align: center; padding: 2rem; color: #28a745;">
                    <h2>✅ All Systems Operational</h2>
                    <p>Your mess management system is running smoothly!</p>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #dc3545;">
                    <h2>⚠️ Issues Detected</h2>
                    <p><?php echo count($health['issues']); ?> issue(s) need attention:</p>
                    <ul style="text-align: left; max-width: 500px; margin: 1rem auto;">
                        <?php foreach ($health['issues'] as $issue): ?>
                            <li><?php echo htmlspecialchars($issue); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Detailed Status -->
    <div class="status-grid">
        <!-- System Information -->
        <div class="status-card">
            <h3>💻 System Information</h3>
            <div class="status-item">
                <span>PHP Version</span>
                <span class="<?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'status-good' : 'status-warning'; ?>">
                    <?php echo $health['php_version']; ?>
                </span>
            </div>
            <div class="status-item">
                <span>MySQL Version</span>
                <span class="status-good"><?php echo $health['mysql_version']; ?></span>
            </div>
            <div class="status-item">
                <span>Database Size</span>
                <span><?php echo $stats['database_size_mb'] ?? 0; ?> MB</span>
            </div>
            <div class="status-item">
                <span>Server Time</span>
                <span><?php echo date('Y-m-d H:i:s'); ?></span>
            </div>
        </div>

        <!-- Database Status -->
        <div class="status-card">
            <h3>🗄️ Database Status</h3>
            <div class="status-item">
                <span>
                    <span class="health-indicator <?php echo $health['database_connection'] ? 'health-good' : 'health-bad'; ?>"></span>
                    Database Connection
                </span>
                <span class="<?php echo $health['database_connection'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $health['database_connection'] ? 'Connected' : 'Failed'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>
                    <span class="health-indicator <?php echo $health['tables_exist'] ? 'health-good' : 'health-bad'; ?>"></span>
                    Tables Setup
                </span>
                <span class="<?php echo $health['tables_exist'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $health['tables_exist'] ? 'Complete' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>
                    <span class="health-indicator <?php echo $health['admin_exists'] ? 'health-good' : 'health-bad'; ?>"></span>
                    Admin Users
                </span>
                <span class="<?php echo $health['admin_exists'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $stats['admin_users'] ?? 0; ?> users
                </span>
            </div>
            <div class="status-item">
                <span>
                    <span class="health-indicator <?php echo $health['server_exists'] ? 'health-good' : 'health-bad'; ?>"></span>
                    Server Users
                </span>
                <span class="<?php echo $health['server_exists'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $stats['server_users'] ?? 0; ?> users
                </span>
            </div>
        </div>

        <!-- Data Statistics -->
        <div class="status-card">
            <h3>📊 Data Statistics</h3>
            <div class="status-item">
                <span>Total Customers</span>
                <span class="status-good"><?php echo $stats['users'] ?? 0; ?></span>
            </div>
            <div class="status-item">
                <span>Total Tokens</span>
                <span class="status-good"><?php echo $stats['tokens'] ?? 0; ?></span>
            </div>
            <div class="status-item">
                <span>Total Payments</span>
                <span class="status-good"><?php echo $stats['payments'] ?? 0; ?></span>
            </div>
            <div class="status-item">
                <span>
                    <span class="health-indicator <?php echo $health['write_permissions'] ? 'health-good' : 'health-bad'; ?>"></span>
                    Write Permissions
                </span>
                <span class="<?php echo $health['write_permissions'] ? 'status-good' : 'status-bad'; ?>">
                    <?php echo $health['write_permissions'] ? 'OK' : 'Failed'; ?>
                </span>
            </div>
        </div>
    </div>

    <!-- System Actions -->
    <div class="card">
        <div class="card-header">
            <h3>🛠️ System Maintenance</h3>
        </div>
        <div class="card-body">
            <div class="action-buttons">
                <div class="action-btn" onclick="showCleanupModal()">
                    <h4>🧹 Cleanup Tokens</h4>
                    <p>Remove old expired tokens</p>
                </div>
                
                <div class="action-btn" onclick="showBackupModal()">
                    <h4>💾 Backup Database</h4>
                    <p>Create database backup</p>
                </div>
                
                <div class="action-btn" onclick="refreshStatus()">
                    <h4>🔄 Refresh Status</h4>
                    <p>Update system information</p>
                </div>
                
                <div class="action-btn" onclick="window.open('../database/setup_helper.php?action=health', '_blank')">
                    <h4>🔍 API Health Check</h4>
                    <p>View raw health data</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="card" style="margin-top: 2rem;">
        <div class="card-header">
            <h3>⚡ Quick Navigation</h3>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <a href="dashboard.php" class="btn btn-primary">📊 Dashboard</a>
                <a href="manage_users.php" class="btn btn-info">👥 Manage Users</a>
                <a href="view_tokens.php" class="btn btn-success">🎫 View Tokens</a>
                <a href="statistics.php" class="btn btn-warning">📈 Statistics</a>
                <a href="../setup.php" class="btn btn-secondary">⚙️ Setup</a>
                <a href="../index.php" class="btn btn-dark">🏠 Home</a>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div id="cleanupModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h3>🧹 Cleanup Expired Tokens</h3>
        <form method="POST">
            <input type="hidden" name="action" value="cleanup_tokens">
            
            <p>Remove tokens that have been pending for more than:</p>
            
            <div style="margin: 1rem 0;">
                <label>
                    <input type="radio" name="days" value="7" checked> 7 days
                </label><br>
                <label>
                    <input type="radio" name="days" value="30"> 30 days
                </label><br>
                <label>
                    <input type="radio" name="days" value="90"> 90 days
                </label>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <button type="submit" class="btn btn-primary">Start Cleanup</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Backup Modal -->
<div id="backupModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h3>💾 Create Database Backup</h3>
        <form method="POST">
            <input type="hidden" name="action" value="backup_database">
            
            <p>This will create a complete backup of your database including:</p>
            <ul>
                <li>All user data</li>
                <li>All tokens and payments</li>
                <li>Admin and server accounts</li>
                <li>System configuration</li>
            </ul>
            
            <p><strong>Note:</strong> The backup will be saved in the database/backups/ directory.</p>
            
            <div style="text-align: center; margin-top: 2rem;">
                <button type="submit" class="btn btn-primary">Create Backup</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<script>
function showCleanupModal() {
    document.getElementById('cleanupModal').style.display = 'block';
}

function showBackupModal() {
    document.getElementById('backupModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('cleanupModal').style.display = 'none';
    document.getElementById('backupModal').style.display = 'none';
}

function refreshStatus() {
    location.reload();
}

// Close modal when clicking outside
window.onclick = function(event) {
    const cleanupModal = document.getElementById('cleanupModal');
    const backupModal = document.getElementById('backupModal');
    
    if (event.target === cleanupModal) {
        cleanupModal.style.display = 'none';
    }
    if (event.target === backupModal) {
        backupModal.style.display = 'none';
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>

<?php require_once '../includes/footer.php'; ?>
