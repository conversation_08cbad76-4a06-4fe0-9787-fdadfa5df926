<?php
// Enhanced Database Setup Script for Mess Management System

// Check if setup is already completed
if (file_exists('.setup_completed')) {
    header('Location: index.php');
    exit();
}

$setup_step = $_GET['step'] ?? 'welcome';
$setup_action = $_POST['action'] ?? '';

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Mess Management System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .setup-container { max-width: 900px; margin: 2rem auto; }
        .setup-steps { display: flex; justify-content: center; margin-bottom: 2rem; }
        .setup-step { padding: 0.5rem 1rem; margin: 0 0.5rem; border-radius: 5px; background: #f8f9fa; }
        .setup-step.active { background: #007bff; color: white; }
        .setup-step.completed { background: #28a745; color: white; }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; }
        .btn-group { text-align: center; margin-top: 2rem; }
        .btn-group .btn { margin: 0 0.5rem; }
        .progress-bar { width: 100%; height: 20px; background: #f8f9fa; border-radius: 10px; margin-bottom: 2rem; }
        .progress-fill { height: 100%; background: #007bff; border-radius: 10px; transition: width 0.3s; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 2rem 0; }
        .feature-card { padding: 1rem; border: 1px solid #ddd; border-radius: 8px; text-align: center; }
        .feature-icon { font-size: 2rem; margin-bottom: 0.5rem; }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="card">
            <div class="card-header">
                <h2>🍽️ Mess Management System Setup</h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php 
                        echo $setup_step === 'welcome' ? '25%' : 
                             ($setup_step === 'database' ? '50%' : 
                             ($setup_step === 'admin' ? '75%' : '100%')); 
                    ?>"></div>
                </div>
            </div>
            <div class="card-body">

<?php
// Handle setup actions
if ($setup_action === 'setup_database') {
    try {
        require_once 'config/config.php';
        
        // Read SQL file
        $sql_file = __DIR__ . '/database/schema.sql';
        
        if (!file_exists($sql_file)) {
            throw new Exception("SQL schema file not found: $sql_file");
        }
        
        $sql_content = file_get_contents($sql_file);
        
        if ($sql_content === false) {
            throw new Exception("Could not read SQL schema file");
        }
        
        // Connect to MySQL server
        $host = $_POST['db_host'] ?? 'localhost';
        $username = $_POST['db_username'] ?? 'root';
        $password = $_POST['db_password'] ?? '';
        
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>✅ Connected to MySQL server successfully.</div>";
        
        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql_content)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        echo "<div class='alert alert-info'>📋 Found " . count($statements) . " SQL statements to execute.</div>";
        
        // Execute each statement
        $success_count = 0;
        foreach ($statements as $index => $statement) {
            if (trim($statement)) {
                try {
                    $pdo->exec($statement);
                    echo "<div style='color: green; margin: 0.5rem 0;'>✓ Statement " . ($index + 1) . " executed successfully</div>";
                    $success_count++;
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'database exists') !== false) {
                        echo "<div style='color: orange; margin: 0.5rem 0;'>⚠ Database already exists, skipping creation</div>";
                        $success_count++;
                    } else {
                        echo "<div style='color: red; margin: 0.5rem 0;'>✗ Error in statement " . ($index + 1) . ": " . $e->getMessage() . "</div>";
                    }
                }
            }
        }
        
        if ($success_count > 0) {
            echo "<div class='alert alert-success'>
                    <h4>🎉 Database setup completed successfully!</h4>
                    <p>Executed $success_count statements successfully.</p>
                </div>";
            
            echo "<div class='btn-group'>
                    <a href='?step=admin' class='btn btn-primary'>Next: Setup Admin Account</a>
                </div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
                <h4>❌ Database Setup Failed!</h4>
                <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
            </div>";
        
        echo "<div class='btn-group'>
                <a href='?step=database' class='btn btn-secondary'>Try Again</a>
            </div>";
    }
    
} elseif ($setup_action === 'create_admin') {
    try {
        require_once 'config/config.php';
        
        $admin_username = $_POST['admin_username'];
        $admin_password = $_POST['admin_password'];
        $admin_email = $_POST['admin_email'];
        $server_username = $_POST['server_username'];
        $server_password = $_POST['server_password'];
        $server_name = $_POST['server_name'];
        
        $conn = getDBConnection();
        
        // Create admin user
        $hashed_admin_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password = ?, email = ?");
        $stmt->execute([$admin_username, $hashed_admin_password, $admin_email, $hashed_admin_password, $admin_email]);
        
        // Create server user
        $hashed_server_password = password_hash($server_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO server_users (username, password, full_name) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password = ?, full_name = ?");
        $stmt->execute([$server_username, $hashed_server_password, $server_name, $hashed_server_password, $server_name]);
        
        echo "<div class='alert alert-success'>
                <h4>🎉 Admin and Server accounts created successfully!</h4>
            </div>";
        
        echo "<div class='btn-group'>
                <a href='?step=complete' class='btn btn-primary'>Complete Setup</a>
            </div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
                <h4>❌ Account Creation Failed!</h4>
                <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
            </div>";
        
        echo "<div class='btn-group'>
                <a href='?step=admin' class='btn btn-secondary'>Try Again</a>
            </div>";
    }
    
} elseif ($setup_step === 'welcome') {
    ?>
    <div style="text-align: center;">
        <h3>🚀 Welcome to Mess Management System Setup</h3>
        <p>This setup wizard will help you configure your mess management system in just a few steps.</p>
    </div>
    
    <div class="feature-grid">
        <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h4>QR Code Integration</h4>
            <p>Easy access via QR code scanning</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">💳</div>
            <h4>Payment Integration</h4>
            <p>Secure payments via Razorpay</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">🎫</div>
            <h4>Token Management</h4>
            <p>Digital token generation and tracking</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h4>Analytics Dashboard</h4>
            <p>Real-time statistics and reports</p>
        </div>
    </div>
    
    <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin: 2rem 0;">
        <h4>📋 Prerequisites Checklist:</h4>
        <ul style="list-style: none; padding: 0;">
            <li>✅ XAMPP/WAMP/LAMP with PHP 7.4+ and MySQL 5.7+</li>
            <li>✅ Web browser with JavaScript enabled</li>
            <li>✅ Razorpay account (optional, for payment integration)</li>
            <li>✅ Write permissions on the web directory</li>
        </ul>
    </div>
    
    <div class="btn-group">
        <a href="?step=database" class="btn btn-primary">Start Setup</a>
        <a href="index.php" class="btn btn-secondary">Skip Setup</a>
    </div>
    
    <?php
} elseif ($setup_step === 'database') {
    ?>
    <h3>🗄️ Database Configuration</h3>
    <p>Configure your MySQL database connection settings.</p>
    
    <form method="POST">
        <input type="hidden" name="action" value="setup_database">
        
        <div class="form-group">
            <label for="db_host">Database Host:</label>
            <input type="text" id="db_host" name="db_host" value="localhost" required>
        </div>
        
        <div class="form-group">
            <label for="db_username">Database Username:</label>
            <input type="text" id="db_username" name="db_username" value="root" required>
        </div>
        
        <div class="form-group">
            <label for="db_password">Database Password:</label>
            <input type="password" id="db_password" name="db_password" placeholder="Leave empty if no password">
        </div>
        
        <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin: 1rem 0;">
            <strong>⚠️ Note:</strong> This will create a new database called 'mess_management' with all required tables.
        </div>
        
        <div class="btn-group">
            <button type="submit" class="btn btn-primary">Setup Database</button>
            <a href="?step=welcome" class="btn btn-secondary">Back</a>
        </div>
    </form>
    
    <?php
} elseif ($setup_step === 'admin') {
    ?>
    <h3>👤 Create Admin & Server Accounts</h3>
    <p>Create your admin and server staff accounts.</p>
    
    <form method="POST">
        <input type="hidden" name="action" value="create_admin">
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
            <div>
                <h4>🔐 Admin Account</h4>
                <div class="form-group">
                    <label for="admin_username">Admin Username:</label>
                    <input type="text" id="admin_username" name="admin_username" value="admin" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">Admin Password:</label>
                    <input type="password" id="admin_password" name="admin_password" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="admin_email">Admin Email:</label>
                    <input type="email" id="admin_email" name="admin_email" required>
                </div>
            </div>
            
            <div>
                <h4>👨‍🍳 Server Account</h4>
                <div class="form-group">
                    <label for="server_username">Server Username:</label>
                    <input type="text" id="server_username" name="server_username" value="server1" required>
                </div>
                
                <div class="form-group">
                    <label for="server_password">Server Password:</label>
                    <input type="password" id="server_password" name="server_password" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="server_name">Server Full Name:</label>
                    <input type="text" id="server_name" name="server_name" value="Server Staff 1" required>
                </div>
            </div>
        </div>
        
        <div class="btn-group">
            <button type="submit" class="btn btn-primary">Create Accounts</button>
            <a href="?step=database" class="btn btn-secondary">Back</a>
        </div>
    </form>
    
    <?php
} elseif ($setup_step === 'complete') {
    // Create setup completion marker
    file_put_contents('.setup_completed', date('Y-m-d H:i:s'));
    ?>
    
    <div style="text-align: center;">
        <h3>🎉 Setup Complete!</h3>
        <p>Your Mess Management System is now ready to use.</p>
    </div>
    
    <div style="background: #d4edda; padding: 1.5rem; border-radius: 8px; margin: 2rem 0;">
        <h4>🔑 Your Login Credentials:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
                <strong>Admin Login:</strong><br>
                URL: <a href="auth/admin_login.php">auth/admin_login.php</a><br>
                Username: <code>admin</code><br>
                Password: <code>[Your chosen password]</code>
            </div>
            <div>
                <strong>Server Login:</strong><br>
                URL: <a href="auth/server_login.php">auth/server_login.php</a><br>
                Username: <code>server1</code><br>
                Password: <code>[Your chosen password]</code>
            </div>
        </div>
    </div>
    
    <div style="background: #fff3cd; padding: 1.5rem; border-radius: 8px; margin: 2rem 0;">
        <h4>📝 Next Steps:</h4>
        <ol>
            <li>Update Razorpay credentials in <code>config/config.php</code></li>
            <li>Customize meal pricing and site settings</li>
            <li>Test the payment flow</li>
            <li>Set up proper file permissions for production</li>
            <li>Enable HTTPS for production deployment</li>
            <li>Delete this setup file for security</li>
        </ol>
    </div>
    
    <div class="btn-group">
        <a href="index.php" class="btn btn-primary">Go to Home Page</a>
        <a href="auth/admin_login.php" class="btn btn-success">Admin Login</a>
        <a href="auth/server_login.php" class="btn btn-info">Server Login</a>
    </div>
    
    <?php
}
?>

            </div>
        </div>
    </div>
    
    <script>
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const passwords = form.querySelectorAll('input[type="password"]');
                    passwords.forEach(password => {
                        if (password.value.length < 6) {
                            e.preventDefault();
                            alert('Password must be at least 6 characters long');
                            password.focus();
                            return false;
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
