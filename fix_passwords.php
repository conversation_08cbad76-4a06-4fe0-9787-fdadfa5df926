<?php
// <PERSON><PERSON><PERSON> to fix password hashing for default users
require_once 'config/config.php';

echo "Fixing password hashing for default users...\n";

try {
    $conn = getDBConnection();
    
    // Fix admin password
    $admin_password = 'admin123';
    $hashed_admin_password = password_hash($admin_password, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
    $result = $stmt->execute([$hashed_admin_password]);
    
    if ($result) {
        echo "✓ Admin password updated successfully\n";
    } else {
        echo "✗ Failed to update admin password\n";
    }
    
    // Fix server password (regenerate hash)
    $server_password = 'server123';
    $hashed_server_password = password_hash($server_password, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE server_users SET password = ? WHERE username = 'server1'");
    $result = $stmt->execute([$hashed_server_password]);
    
    if ($result) {
        echo "✓ Server password updated successfully\n";
    } else {
        echo "✗ Failed to update server password\n";
    }
    
    echo "\nPassword fix completed!\n";
    echo "You can now login with:\n";
    echo "Admin - Username: admin, Password: admin123\n";
    echo "Server - Username: server1, Password: server123\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
