<?php
header('Content-Type: application/json');
require_once '../config/config.php';
require_once '../includes/functions.php';

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $conn = getDBConnection();

    if ($method === 'POST') {
        $action = $input['action'] ?? '';

        switch ($action) {
            case 'serve_token':
                handleServeToken($conn, $input);
                break;

            case 'delete_token':
                handleDeleteToken($conn, $input);
                break;

            case 'search_token':
                handleSearchToken($conn, $input);
                break;

            default:
                sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
    } elseif ($method === 'GET') {
        $action = $_GET['action'] ?? '';

        switch ($action) {
            case 'get_pending_count':
                handleGetPendingCount($conn);
                break;

            default:
                sendJsonResponse(['success' => false, 'message' => 'Invalid action'], 400);
        }
    } else {
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logActivity("API error: " . $e->getMessage());
    sendJsonResponse(['success' => false, 'message' => 'Internal server error'], 500);
}

function handleServeToken($conn, $input) {
    try {
        // Check authentication
        if (!isServer() && !isAdmin()) {
            sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $token_id = $input['token_id'] ?? '';

        if (empty($token_id)) {
            sendJsonResponse(['success' => false, 'message' => 'Token ID is required'], 400);
        }

        // Get token details
        $token = getTokenByTokenId($token_id);

        if (!$token) {
            sendJsonResponse(['success' => false, 'message' => 'Token not found'], 404);
        }

        if ($token['status'] !== 'Pending') {
            sendJsonResponse(['success' => false, 'message' => 'Token has already been processed. Current status: ' . $token['status']], 400);
        }

        // Mark token as served
        $served_by = isServer() ? $_SESSION['server_id'] : null;

        // Validate server_id exists if provided
        if ($served_by !== null) {
            $stmt = $conn->prepare("SELECT id FROM server_users WHERE id = ?");
            $stmt->execute([$served_by]);
            if (!$stmt->fetch()) {
                error_log("Invalid server_id: $served_by");
                $served_by = null;
            }
        }

        $success = updateTokenStatus($token_id, 'Served', $served_by);

        if ($success) {
            $user_type = isServer() ? 'server' : 'admin';
            $user_id = isServer() ? $_SESSION['server_id'] : $_SESSION['admin_id'];
            logActivity("Token served: $token_id for user: " . $token['name'], $user_type, $user_id);

            // Get updated token data
            $updated_token = getTokenByTokenId($token_id);

            sendJsonResponse([
                'success' => true,
                'message' => 'Token marked as served successfully',
                'token' => $updated_token
            ]);
        } else {
            error_log("Failed to update token status for token_id: $token_id");
            sendJsonResponse(['success' => false, 'message' => 'Failed to update token status. Please check server logs.'], 500);
        }

    } catch (Exception $e) {
        error_log("Error in handleServeToken: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Internal server error: ' . $e->getMessage()], 500);
    }
}

function handleDeleteToken($conn, $input) {
    // Check admin authentication
    if (!isAdmin()) {
        sendJsonResponse(['success' => false, 'message' => 'Admin access required'], 401);
    }

    $token_id = $input['token_id'] ?? '';

    if (empty($token_id)) {
        sendJsonResponse(['success' => false, 'message' => 'Token ID is required'], 400);
    }

    // Get token details for logging
    $token = getTokenByTokenId($token_id);

    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'Token not found'], 404);
    }

    // Delete token
    $stmt = $conn->prepare("DELETE FROM tokens WHERE token_id = ?");
    $success = $stmt->execute([$token_id]);

    if ($success) {
        logActivity("Token deleted: $token_id for user: " . $token['name'], 'admin', $_SESSION['admin_id']);
        sendJsonResponse(['success' => true, 'message' => 'Token deleted successfully']);
    } else {
        sendJsonResponse(['success' => false, 'message' => 'Failed to delete token'], 500);
    }
}

function handleSearchToken($conn, $input) {
    // Check authentication
    if (!isServer() && !isAdmin()) {
        sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
    }

    $token_id = $input['token_id'] ?? '';

    if (empty($token_id)) {
        sendJsonResponse(['success' => false, 'message' => 'Token ID is required'], 400);
    }

    $token = getTokenByTokenId($token_id);

    if ($token) {
        sendJsonResponse(['success' => true, 'token' => $token]);
    } else {
        sendJsonResponse(['success' => false, 'message' => 'Token not found'], 404);
    }
}

function handleGetPendingCount($conn) {
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM tokens
        WHERE DATE(created_at) = CURDATE() AND status = 'Pending'
    ");
    $stmt->execute();
    $count = $stmt->fetch()['count'];

    sendJsonResponse(['success' => true, 'count' => (int)$count]);
}
?>
