# Mess Management System

A complete web-based system for automating the mess food coupon process with QR code integration, online payments, and real-time token management.

## Features

### 🟩 Key Modules
1. **QR Code-Based Form Access** - Easy access via QR code scanning
2. **Online Form Submission** - Collect user details (Name, UID, Phone, etc.)
3. **Payment Integration** - Secure payments via Razorpay or UPI
4. **Token Generation** - Unique UUID-based tokens after successful payment
5. **Database Management** - Store tokens with user info and status tracking

### 🟧 User Flow
1. Scan QR code → Open form
2. Fill in details → Redirect to payment
3. After successful payment → Show unique token
4. Store token as "Pending" in database
5. Server dashboard shows real-time tokens
6. Staff searches token and marks as "Served"
7. Token becomes unusable after serving

### 🟦 Admin Dashboard
- Secure admin login
- View all token entries with status (Pending/Served)
- Daily token statistics and analytics
- Filter by date, user UID, token ID
- Delete invalid entries
- Revenue tracking

### 🟨 Server (Mess Staff) Dashboard
- Staff login system
- Search by token ID or scan QR code
- Mark tokens as "Served"
- Real-time pending token list
- One-time use token validation

## Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Payment**: Razorpay Integration
- **Server**: Apache (XAMPP recommended)

## Installation

### Prerequisites
- XAMPP/WAMP/LAMP with PHP 7.4+ and MySQL 5.7+
- Web browser with JavaScript enabled
- Razorpay account (for payment integration)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   # Place the project in your web server directory
   # For XAMPP: C:\xampp\htdocs\messmangment\
   ```

2. **Start your web server**
   - Start Apache and MySQL in XAMPP Control Panel

3. **Run the setup script**
   - Open your browser and navigate to: `http://localhost/messmangment/setup.php`
   - Follow the on-screen instructions
   - The setup will create the database and tables automatically

4. **Configure Razorpay (Optional)**
   - Edit `config/config.php`
   - Update `RAZORPAY_KEY_ID` and `RAZORPAY_KEY_SECRET` with your credentials
   - For testing, you can use the demo mode

5. **Access the system**
   - Home page: `http://localhost/messmangment/`
   - Admin login: `http://localhost/messmangment/auth/admin_login.php`
   - Server login: `http://localhost/messmangment/auth/server_login.php`

### Default Credentials

**Admin Login:**
- Username: `admin`
- Password: `admin123`

**Server Login:**
- Username: `server1`
- Password: `server123`

> ⚠️ **Important**: Change these default passwords after first login!

## File Structure

```
messmangment/
├── index.php                 # QR code landing page
├── setup.php                 # Database setup script
├── README.md                 # This file
├── config/
│   ├── database.php          # Database configuration
│   └── config.php            # Application settings
├── auth/
│   ├── admin_login.php       # Admin authentication
│   ├── server_login.php      # Server staff authentication
│   └── logout.php            # Logout handler
├── user/
│   ├── form.php              # User registration form
│   ├── payment.php           # Payment processing
│   ├── payment_success.php   # Success page with token
│   └── payment_failed.php    # Payment failure page
├── admin/
│   ├── dashboard.php         # Admin dashboard
│   ├── view_tokens.php       # Token management
│   └── statistics.php        # Analytics and reports
├── server/
│   ├── dashboard.php         # Server staff dashboard
│   ├── search_token.php      # Token search functionality
│   └── mark_served.php       # Mark tokens as served
├── api/
│   ├── token_operations.php  # Token API endpoints
│   └── payment_callback.php  # Payment webhook handler
├── assets/
│   ├── css/style.css         # Main stylesheet
│   ├── js/script.js          # JavaScript functionality
│   └── images/               # Image assets
├── includes/
│   ├── header.php            # Common header
│   ├── footer.php            # Common footer
│   └── functions.php         # Utility functions
└── database/
    └── schema.sql            # Database schema
```

## Database Schema

### Tables
1. **users** - Customer information
2. **tokens** - Token details and status
3. **payments** - Payment transaction records
4. **admin_users** - Admin login credentials
5. **server_users** - Server staff credentials

## Configuration

### Payment Settings
Edit `config/config.php` to configure:
- Razorpay API keys
- Meal pricing
- Site URL and name
- Email settings

### Database Settings
Edit `config/database.php` for:
- Database host, name, username, password
- Connection settings

## Usage

### For Customers
1. Scan QR code or visit the website
2. Fill in personal details (Name, UID, Phone)
3. Proceed to payment
4. After successful payment, save the token
5. Show token at mess counter to collect food

### For Mess Staff
1. Login to server dashboard
2. Search for tokens by ID or customer details
3. Verify customer identity
4. Mark token as "Served"
5. Token becomes invalid for future use

### For Administrators
1. Login to admin dashboard
2. Monitor all tokens and their status
3. View daily statistics and revenue
4. Manage user accounts and tokens
5. Generate reports

## Security Features

- Password hashing with PHP's `password_hash()`
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- Session management with secure cookies
- CSRF protection (recommended for production)
- Input validation on both client and server side

## API Endpoints

### Token Operations (`/api/token_operations.php`)
- `POST /serve_token` - Mark token as served
- `POST /delete_token` - Delete token (admin only)
- `POST /search_token` - Search for token
- `GET /get_pending_count` - Get pending token count

## Customization

### Styling
- Edit `assets/css/style.css` for visual customization
- Responsive design works on mobile devices
- Print-friendly styles included

### Functionality
- Modify `includes/functions.php` for business logic
- Update `assets/js/script.js` for client-side features
- Extend API endpoints in `api/` directory

## Production Deployment

### Security Checklist
- [ ] Change default passwords
- [ ] Update Razorpay credentials
- [ ] Enable HTTPS
- [ ] Set proper file permissions
- [ ] Disable error reporting
- [ ] Remove setup.php file
- [ ] Configure backup strategy
- [ ] Set up monitoring

### Performance Optimization
- Enable PHP OPcache
- Use CDN for static assets
- Implement database indexing
- Add caching mechanisms
- Optimize images

## Troubleshooting

### Common Issues

**Database Connection Failed**
- Check XAMPP MySQL is running
- Verify database credentials in `config/database.php`
- Ensure database exists

**Payment Not Working**
- Verify Razorpay credentials
- Check network connectivity
- Review browser console for errors

**Tokens Not Generating**
- Check database permissions
- Verify payment completion
- Review server error logs

**Login Issues**
- Confirm default credentials
- Check session configuration
- Clear browser cache

## Support

For technical support or feature requests:
- Check the troubleshooting section
- Review server error logs
- Verify configuration settings

## License

This project is open source and available under the MIT License.

## Changelog

### Version 1.0.0
- Initial release
- Complete mess management system
- QR code integration
- Payment processing
- Admin and server dashboards
- Real-time token management
- Statistics and reporting

---

**Note**: This system is designed for educational and small-scale commercial use. For large-scale deployment, consider additional security measures and performance optimizations.
