-- Mess Management System Database Schema

CREATE DATABASE IF NOT EXISTS mess_management;
USE mess_management;

-- Disable foreign key checks during table creation
SET FOREIGN_KEY_CHECKS = 0;

-- Users table to store customer information (must be created first due to foreign keys)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    uid VARCHAR(50) NOT NULL,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Server users table (mess staff) - must be created before tokens table
CREATE TABLE IF NOT EXISTS server_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Payments table to store payment information
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    token_id VARCHAR(100),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status ENUM('created', 'authorized', 'captured', 'refunded', 'failed') DEFAULT 'created',
    razorpay_payment_id VARCHAR(100),
    razorpay_order_id VARCHAR(100),
    razorpay_signature VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tokens table to store coupon tokens
CREATE TABLE IF NOT EXISTS tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    payment_id VARCHAR(100),
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('Pending', 'Served', 'Expired') DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    served_at TIMESTAMP NULL,
    served_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (served_by) REFERENCES server_users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tokens_status ON tokens(status);
CREATE INDEX IF NOT EXISTS idx_tokens_created_at ON tokens(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_users_uid ON users(uid);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO admin_users (username, password, email) VALUES
('admin', '$2y$10$wiI0nGS14ZZ61muCanPArezLyKrh503nNs8tfMyxP3ZnJZ4zE.KnK', '<EMAIL>');

-- Insert default server user (password: server123)
INSERT IGNORE INTO server_users (username, password, full_name) VALUES
('server1', '$2y$10$tNwbTOUxg4/XDQp8HlITv.lZNHi99ScO7iGWCzimcmSflzSsn.Kt6', 'Server Staff 1');
