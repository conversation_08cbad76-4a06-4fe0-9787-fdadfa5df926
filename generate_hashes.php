<?php
// Generate proper password hashes for the schema file
$admin_password = 'admin123';
$server_password = 'server123';

$admin_hash = password_hash($admin_password, PASSWORD_DEFAULT);
$server_hash = password_hash($server_password, PASSWORD_DEFAULT);

echo "Admin password hash for 'admin123':\n";
echo $admin_hash . "\n\n";

echo "Server password hash for 'server123':\n";
echo $server_hash . "\n\n";

echo "SQL statements to update schema.sql:\n";
echo "-- Insert default admin user (password: admin123)\n";
echo "INSERT IGNORE INTO admin_users (username, password, email) VALUES\n";
echo "('admin', '$admin_hash', '<EMAIL>');\n\n";

echo "-- Insert default server user (password: server123)\n";
echo "INSERT IGNORE INTO server_users (username, password, full_name) VALUES\n";
echo "('server1', '$server_hash', 'Server Staff 1');\n";
?>
