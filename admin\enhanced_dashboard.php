<?php
$page_title = 'Enhanced Admin Dashboard';
$show_nav = true;
require_once '../includes/header.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

try {
    $conn = getDBConnection();
    
    // Get comprehensive statistics
    $stats = [];
    
    // Today's statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_tokens,
            SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as pending_tokens,
            SUM(CASE WHEN status = 'Served' THEN 1 ELSE 0 END) as served_tokens,
            SUM(CASE WHEN status = 'Expired' THEN 1 ELSE 0 END) as expired_tokens,
            SUM(amount) as total_revenue
        FROM tokens 
        WHERE DATE(created_at) = CURDATE()
    ");
    $stmt->execute();
    $today_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // This week's statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_tokens,
            SUM(amount) as total_revenue
        FROM tokens 
        WHERE YEARWEEK(created_at) = YEARWEEK(NOW())
    ");
    $stmt->execute();
    $week_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // This month's statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_tokens,
            SUM(amount) as total_revenue
        FROM tokens 
        WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())
    ");
    $stmt->execute();
    $month_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Recent tokens
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone, s.full_name as served_by_name
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        LEFT JOIN server_users s ON t.served_by = s.id
        ORDER BY t.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent_tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Top customers this month
    $stmt = $conn->prepare("
        SELECT u.name, u.uid, u.phone, COUNT(t.id) as token_count, SUM(t.amount) as total_spent
        FROM users u
        JOIN tokens t ON u.id = t.user_id
        WHERE YEAR(t.created_at) = YEAR(NOW()) AND MONTH(t.created_at) = MONTH(NOW())
        GROUP BY u.id
        ORDER BY token_count DESC, total_spent DESC
        LIMIT 5
    ");
    $stmt->execute();
    $top_customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Server performance today
    $stmt = $conn->prepare("
        SELECT s.full_name, COUNT(t.id) as tokens_served
        FROM server_users s
        LEFT JOIN tokens t ON s.id = t.served_by AND DATE(t.served_at) = CURDATE()
        GROUP BY s.id
        ORDER BY tokens_served DESC
    ");
    $stmt->execute();
    $server_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Hourly distribution today
    $stmt = $conn->prepare("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as count
        FROM tokens 
        WHERE DATE(created_at) = CURDATE()
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $stmt->execute();
    $hourly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading dashboard data.';
    logActivity("Admin dashboard error: " . $e->getMessage());
    $today_stats = ['total_tokens' => 0, 'pending_tokens' => 0, 'served_tokens' => 0, 'expired_tokens' => 0, 'total_revenue' => 0];
    $week_stats = ['total_tokens' => 0, 'total_revenue' => 0];
    $month_stats = ['total_tokens' => 0, 'total_revenue' => 0];
    $recent_tokens = [];
    $top_customers = [];
    $server_performance = [];
    $hourly_data = [];
}
?>

<style>
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.action-btn {
    display: block;
    padding: 1rem;
    text-align: center;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #e9ecef;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
}

.chart-container {
    height: 300px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-container {
    max-height: 400px;
    overflow-y: auto;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-served {
    background: #d4edda;
    color: #155724;
}

.status-expired {
    background: #f8d7da;
    color: #721c24;
}
</style>

<div class="dashboard-header" style="text-align: center; margin-bottom: 2rem;">
    <h2>🏢 Admin Dashboard</h2>
    <p style="color: #666;">Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>! Here's your mess overview.</p>
</div>

<!-- Today's Statistics -->
<div class="dashboard-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo $today_stats['total_tokens']; ?></div>
        <div class="stat-label">📊 Today's Tokens</div>
    </div>
    
    <div class="stat-card warning">
        <div class="stat-number"><?php echo $today_stats['pending_tokens']; ?></div>
        <div class="stat-label">⏳ Pending Tokens</div>
    </div>
    
    <div class="stat-card success">
        <div class="stat-number"><?php echo $today_stats['served_tokens']; ?></div>
        <div class="stat-label">✅ Served Tokens</div>
    </div>
    
    <div class="stat-card info">
        <div class="stat-number"><?php echo formatCurrency($today_stats['total_revenue']); ?></div>
        <div class="stat-label">💰 Today's Revenue</div>
    </div>
</div>

<!-- Period Comparison -->
<div class="card">
    <div class="card-header">
        <h3>📈 Performance Overview</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                <h4>This Week</h4>
                <p><strong><?php echo $week_stats['total_tokens']; ?></strong> tokens</p>
                <p><strong><?php echo formatCurrency($week_stats['total_revenue']); ?></strong> revenue</p>
            </div>
            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                <h4>This Month</h4>
                <p><strong><?php echo $month_stats['total_tokens']; ?></strong> tokens</p>
                <p><strong><?php echo formatCurrency($month_stats['total_revenue']); ?></strong> revenue</p>
            </div>
            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                <h4>Completion Rate</h4>
                <p><strong><?php 
                    $completion_rate = $today_stats['total_tokens'] > 0 ? 
                        round(($today_stats['served_tokens'] / $today_stats['total_tokens']) * 100, 1) : 0;
                    echo $completion_rate; 
                ?>%</strong> today</p>
                <p style="color: <?php echo $completion_rate >= 80 ? '#28a745' : ($completion_rate >= 60 ? '#ffc107' : '#dc3545'); ?>;">
                    <?php echo $completion_rate >= 80 ? 'Excellent' : ($completion_rate >= 60 ? 'Good' : 'Needs Attention'); ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        <h3>⚡ Quick Actions</h3>
    </div>
    <div class="card-body">
        <div class="quick-actions">
            <a href="view_tokens.php?status=Pending" class="action-btn">
                <strong>⏳ Pending Tokens</strong><br>
                <span style="color: #dc3545;"><?php echo $today_stats['pending_tokens']; ?> waiting</span>
            </a>
            <a href="view_tokens.php?date=<?php echo date('Y-m-d'); ?>" class="action-btn">
                <strong>📅 Today's Orders</strong><br>
                <span style="color: #007bff;"><?php echo $today_stats['total_tokens']; ?> total</span>
            </a>
            <a href="statistics.php" class="action-btn">
                <strong>📊 Analytics</strong><br>
                <span style="color: #28a745;">View Reports</span>
            </a>
            <a href="manage_users.php" class="action-btn">
                <strong>👥 Manage Users</strong><br>
                <span style="color: #6f42c1;">Admin & Staff</span>
            </a>
            <a href="../index.php" class="action-btn">
                <strong>🔗 QR Code Page</strong><br>
                <span style="color: #17a2b8;">Customer Entry</span>
            </a>
            <a href="../config/config.php" class="action-btn">
                <strong>⚙️ Settings</strong><br>
                <span style="color: #fd7e14;">Configure</span>
            </a>
        </div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-top: 2rem;">
    <!-- Recent Tokens -->
    <div class="card">
        <div class="card-header">
            <h3>🎫 Recent Tokens</h3>
        </div>
        <div class="card-body">
            <?php if (empty($recent_tokens)): ?>
                <p style="text-align: center; color: #666; padding: 2rem;">No tokens found.</p>
            <?php else: ?>
                <div class="table-container">
                    <table class="table" style="font-size: 0.9rem;">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_tokens as $token): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($token['name']); ?></strong><br>
                                        <small style="color: #666;"><?php echo htmlspecialchars($token['uid']); ?></small>
                                    </td>
                                    <td><?php echo formatCurrency($token['amount']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower($token['status']); ?>">
                                            <?php echo $token['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo date('H:i', strtotime($token['created_at'])); ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Top Customers -->
    <div class="card">
        <div class="card-header">
            <h3>🏆 Top Customers (This Month)</h3>
        </div>
        <div class="card-body">
            <?php if (empty($top_customers)): ?>
                <p style="text-align: center; color: #666; padding: 2rem;">No customer data available.</p>
            <?php else: ?>
                <div class="table-container">
                    <table class="table" style="font-size: 0.9rem;">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Orders</th>
                                <th>Spent</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_customers as $index => $customer): ?>
                                <tr>
                                    <td>
                                        <?php if ($index === 0): ?>
                                            <span style="color: #ffd700;">🥇</span>
                                        <?php elseif ($index === 1): ?>
                                            <span style="color: #c0c0c0;">🥈</span>
                                        <?php elseif ($index === 2): ?>
                                            <span style="color: #cd7f32;">🥉</span>
                                        <?php else: ?>
                                            <span><?php echo $index + 1; ?>.</span>
                                        <?php endif; ?>
                                        <strong><?php echo htmlspecialchars($customer['name']); ?></strong><br>
                                        <small style="color: #666;"><?php echo htmlspecialchars($customer['uid']); ?></small>
                                    </td>
                                    <td><strong><?php echo $customer['token_count']; ?></strong></td>
                                    <td><?php echo formatCurrency($customer['total_spent']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Server Performance -->
<div class="card" style="margin-top: 1.5rem;">
    <div class="card-header">
        <h3>👨‍🍳 Server Performance (Today)</h3>
    </div>
    <div class="card-body">
        <?php if (empty($server_performance)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No server data available.</p>
        <?php else: ?>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <?php foreach ($server_performance as $server): ?>
                    <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                        <h4><?php echo htmlspecialchars($server['full_name']); ?></h4>
                        <p style="font-size: 1.5rem; font-weight: bold; color: #007bff;">
                            <?php echo $server['tokens_served']; ?>
                        </p>
                        <p style="color: #666;">tokens served</p>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Hourly Distribution Chart -->
<div class="card" style="margin-top: 1.5rem;">
    <div class="card-header">
        <h3>⏰ Today's Hourly Distribution</h3>
    </div>
    <div class="card-body">
        <?php if (empty($hourly_data)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No hourly data available for today.</p>
        <?php else: ?>
            <div style="display: flex; align-items: end; justify-content: space-between; height: 200px; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                <?php 
                $max_count = max(array_column($hourly_data, 'count'));
                for ($hour = 0; $hour < 24; $hour++): 
                    $count = 0;
                    foreach ($hourly_data as $data) {
                        if ($data['hour'] == $hour) {
                            $count = $data['count'];
                            break;
                        }
                    }
                    $height = $max_count > 0 ? ($count / $max_count) * 150 : 0;
                ?>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="background: #007bff; width: 20px; height: <?php echo $height; ?>px; margin-bottom: 5px; border-radius: 2px;" title="<?php echo $count; ?> tokens at <?php echo $hour; ?>:00"></div>
                        <small style="writing-mode: vertical-rl; text-orientation: mixed;"><?php echo $hour; ?></small>
                    </div>
                <?php endfor; ?>
            </div>
            <p style="text-align: center; margin-top: 1rem; color: #666;">
                <small>Peak hour: <?php 
                    $peak_hour = 0;
                    $peak_count = 0;
                    foreach ($hourly_data as $data) {
                        if ($data['count'] > $peak_count) {
                            $peak_count = $data['count'];
                            $peak_hour = $data['hour'];
                        }
                    }
                    echo $peak_hour . ':00 (' . $peak_count . ' tokens)';
                ?></small>
            </p>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-refresh dashboard every 60 seconds
setInterval(function() {
    location.reload();
}, 60000);

// Add real-time clock
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const dateString = now.toLocaleDateString();
    
    // Add clock to header if it doesn't exist
    let clockElement = document.getElementById('live-clock');
    if (!clockElement) {
        clockElement = document.createElement('div');
        clockElement.id = 'live-clock';
        clockElement.style.cssText = 'position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 0.5rem; border-radius: 5px; font-family: monospace; z-index: 1000;';
        document.body.appendChild(clockElement);
    }
    
    clockElement.innerHTML = `${dateString}<br>${timeString}`;
}

// Update clock every second
setInterval(updateClock, 1000);
updateClock();
</script>

<?php require_once '../includes/footer.php'; ?>
