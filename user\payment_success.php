<?php
$page_title = 'Payment Successful';
$show_nav = true;
require_once '../includes/header.php';

// Check if payment data exists
if (!isset($_SESSION['payment_id']) && !isset($_GET['payment_id'])) {
    $_SESSION['error_message'] = 'Invalid payment session.';
    redirect('../index.php');
}

$payment_id = $_SESSION['payment_id'] ?? $_GET['payment_id'];
$is_demo = isset($_GET['demo']) && $_GET['demo'] === 'true';

// Handle POST request from payment verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $razorpay_payment_id = $_POST['razorpay_payment_id'] ?? '';
    $razorpay_order_id = $_POST['razorpay_order_id'] ?? '';
    $razorpay_signature = $_POST['razorpay_signature'] ?? '';
    $payment_id = $_POST['payment_id'] ?? '';

    if (!empty($razorpay_payment_id) && !empty($razorpay_order_id) && !empty($razorpay_signature) && !empty($payment_id)) {
        // Verify signature
        if (verifyRazorpaySignature($razorpay_order_id, $razorpay_payment_id, $razorpay_signature)) {
            // Update payment status
            $conn = getDBConnection();
            $stmt = $conn->prepare("
                UPDATE payments
                SET status = 'captured',
                    razorpay_payment_id = ?,
                    razorpay_signature = ?,
                    updated_at = NOW()
                WHERE payment_id = ? AND razorpay_order_id = ?
            ");
            $stmt->execute([$razorpay_payment_id, $razorpay_signature, $payment_id, $razorpay_order_id]);
        } else {
            $_SESSION['error_message'] = 'Payment verification failed.';
            redirect('payment_failed.php?error=verification_failed');
        }
    }
}

try {
    $conn = getDBConnection();

    // Get payment details
    $stmt = $conn->prepare("
        SELECT p.*, u.name, u.uid, u.phone
        FROM payments p
        JOIN users u ON p.user_id = u.id
        WHERE p.payment_id = ?
    ");
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$payment) {
        $_SESSION['error_message'] = 'Payment not found.';
        redirect('../index.php');
    }

    // Check if payment is captured
    if ($payment['status'] !== 'captured' && !$is_demo) {
        $_SESSION['error_message'] = 'Payment not completed successfully.';
        redirect('payment_failed.php?error=payment_not_captured');
    }

    // Generate token if payment is successful and token doesn't exist
    $stmt = $conn->prepare("SELECT token_id FROM tokens WHERE payment_id = ?");
    $stmt->execute([$payment_id]);
    $existing_token = $stmt->fetch();

    if (!$existing_token) {
        // Generate unique token
        $token_id = generateToken();

        // Insert token
        $stmt = $conn->prepare("
            INSERT INTO tokens (token_id, user_id, payment_id, amount, status)
            VALUES (?, ?, ?, ?, 'Pending')
        ");
        $stmt->execute([$token_id, $payment['user_id'], $payment_id, $payment['amount']]);

        // Update payment with token ID if not already set
        if (empty($payment['token_id'])) {
            $stmt = $conn->prepare("
                UPDATE payments
                SET token_id = ?, updated_at = NOW()
                WHERE payment_id = ?
            ");
            $stmt->execute([$token_id, $payment_id]);
        }

        logActivity("Token generated: $token_id for user: " . $payment['name'], 'user', $payment['user_id']);
    } else {
        $token_id = $existing_token['token_id'];
    }

    // Clear session data
    unset($_SESSION['order_data']);
    unset($_SESSION['payment_id']);

} catch (Exception $e) {
    $_SESSION['error_message'] = 'An error occurred while processing your payment.';
    logActivity("Payment success processing error: " . $e->getMessage());
    redirect('../index.php');
}
?>

<div class="card">
    <div class="card-header" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
        <h2>🎉 Payment Successful!</h2>
    </div>
    <div class="card-body">
        <?php if ($is_demo): ?>
            <div class="alert alert-info">
                <strong>Demo Mode:</strong> This is a demonstration. In production, payment would be processed through Razorpay.
            </div>
        <?php endif; ?>

        <div class="token-display">
            <h3>Your Food Token</h3>
            <div class="token-id"><?php echo htmlspecialchars($token_id); ?></div>
            <p>Show this token at the mess counter to collect your food</p>
            <button class="btn btn-secondary copy-token-btn" data-token-id="<?php echo htmlspecialchars($token_id); ?>">
                📋 Copy Token
            </button>
        </div>

        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 5px; margin: 2rem 0;">
            <h4 style="margin: 0 0 1rem 0; color: #667eea;">Order Summary</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div>
                    <strong>Name:</strong> <?php echo htmlspecialchars($payment['name']); ?>
                </div>
                <div>
                    <strong>UID:</strong> <?php echo htmlspecialchars($payment['uid']); ?>
                </div>
                <div>
                    <strong>Phone:</strong> <?php echo htmlspecialchars($payment['phone']); ?>
                </div>
                <div>
                    <strong>Amount Paid:</strong> <?php echo formatCurrency($payment['amount']); ?>
                </div>
                <div>
                    <strong>Payment ID:</strong> <?php echo htmlspecialchars($payment_id); ?>
                </div>
                <div>
                    <strong>Status:</strong> <span class="badge badge-warning">Pending</span>
                </div>
            </div>
        </div>

        <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; border-left: 4px solid #ffc107; margin: 2rem 0;">
            <h4 style="margin: 0 0 0.5rem 0; color: #856404;">Important Instructions</h4>
            <ul style="margin: 0; padding-left: 1.5rem; color: #856404;">
                <li><strong>Save this token:</strong> Take a screenshot or write down the token ID</li>
                <li><strong>Show at counter:</strong> Present this token to the mess staff</li>
                <li><strong>One-time use:</strong> Token can only be used once</li>
                <li><strong>Valid today:</strong> Token expires at the end of the day</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="../index.php" class="btn btn-primary">Order Another Meal</a>
            <button onclick="window.print()" class="btn btn-secondary">🖨️ Print Token</button>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .nav, .alert {
        display: none !important;
    }

    .token-display {
        border: 2px solid #000;
        padding: 2rem;
        text-align: center;
    }

    .token-id {
        font-size: 2rem;
        font-weight: bold;
        border: 1px solid #000;
        padding: 1rem;
        margin: 1rem 0;
    }
}
</style>

<script>
// Auto-copy token to clipboard on page load
document.addEventListener('DOMContentLoaded', function() {
    const tokenId = '<?php echo $token_id; ?>';

    // Show success message
    setTimeout(() => {
        MessSystem.showAlert('Token generated successfully! Token ID copied to clipboard.', 'success');
    }, 500);
});

// Add QR code for token (optional enhancement)
// You can integrate a QR code library to generate QR code for the token
</script>

<?php require_once '../includes/footer.php'; ?>
