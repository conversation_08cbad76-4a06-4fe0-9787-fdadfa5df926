<?php
$page_title = 'Enhanced Server Dashboard';
$show_nav = true;
require_once '../includes/header.php';

// Check server authentication
if (!isServer()) {
    $_SESSION['error_message'] = 'Please login as server staff to access this page.';
    redirect('../auth/server_login.php');
}

try {
    $conn = getDBConnection();
    
    // Get today's statistics
    $stats = [];
    
    // Pending tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Pending'
    ");
    $stmt->execute();
    $stats['pending_tokens'] = $stmt->fetch()['count'];
    
    // Served tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Served'
    ");
    $stmt->execute();
    $stats['served_tokens'] = $stmt->fetch()['count'];
    
    // Tokens served by this server today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(served_at) = CURDATE() AND served_by = ?
    ");
    $stmt->execute([$_SESSION['server_id']]);
    $stats['my_served_tokens'] = $stmt->fetch()['count'];
    
    // Total tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE()
    ");
    $stmt->execute();
    $stats['total_tokens'] = $stmt->fetch()['count'];
    
    // Recent pending tokens
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone 
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'Pending'
        ORDER BY t.created_at ASC 
        LIMIT 15
    ");
    $stmt->execute();
    $pending_tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recently served tokens by this server
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone 
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.served_by = ? AND DATE(t.served_at) = CURDATE()
        ORDER BY t.served_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['server_id']]);
    $my_served_tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Server performance comparison
    $stmt = $conn->prepare("
        SELECT s.full_name, COUNT(t.id) as tokens_served
        FROM server_users s
        LEFT JOIN tokens t ON s.id = t.served_by AND DATE(t.served_at) = CURDATE()
        GROUP BY s.id
        ORDER BY tokens_served DESC
    ");
    $stmt->execute();
    $server_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading dashboard data.';
    logActivity("Server dashboard error: " . $e->getMessage());
    $stats = ['pending_tokens' => 0, 'served_tokens' => 0, 'my_served_tokens' => 0, 'total_tokens' => 0];
    $pending_tokens = [];
    $my_served_tokens = [];
    $server_performance = [];
}
?>

<style>
.server-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card.pending {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.served {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.personal {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.token-search {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.search-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
}

.token-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
}

.token-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.token-card.pending {
    border-left: 4px solid #dc3545;
}

.token-card.served {
    border-left: 4px solid #28a745;
}

.serve-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s;
}

.serve-btn:hover {
    background: #218838;
}

.serve-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.action-btn {
    display: block;
    padding: 1rem;
    text-align: center;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #e9ecef;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
}
</style>

<div class="server-dashboard">
    <div class="dashboard-header" style="text-align: center; margin-bottom: 2rem;">
        <h2>👨‍🍳 Server Dashboard</h2>
        <p style="color: #666;">Welcome, <?php echo htmlspecialchars($_SESSION['server_name']); ?>! Ready to serve some meals?</p>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card pending">
            <div class="stat-number"><?php echo $stats['pending_tokens']; ?></div>
            <div class="stat-label">⏳ Pending Orders</div>
        </div>
        
        <div class="stat-card served">
            <div class="stat-number"><?php echo $stats['served_tokens']; ?></div>
            <div class="stat-label">✅ Total Served Today</div>
        </div>
        
        <div class="stat-card personal">
            <div class="stat-number"><?php echo $stats['my_served_tokens']; ?></div>
            <div class="stat-label">🏆 My Served Today</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_tokens']; ?></div>
            <div class="stat-label">📊 Total Orders Today</div>
        </div>
    </div>

    <!-- Token Search -->
    <div class="token-search">
        <h3>🔍 Quick Token Search</h3>
        <input type="text" id="tokenSearch" class="search-input" placeholder="Enter token ID to search and serve...">
        <div id="searchResult"></div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            <h3>⚡ Quick Actions</h3>
        </div>
        <div class="card-body">
            <div class="quick-actions">
                <a href="#pending-tokens" class="action-btn" onclick="scrollToPending()">
                    <strong>⏳ View Pending</strong><br>
                    <span style="color: #dc3545;"><?php echo $stats['pending_tokens']; ?> waiting</span>
                </a>
                <a href="search_token.php" class="action-btn">
                    <strong>🔍 Advanced Search</strong><br>
                    <span style="color: #007bff;">Find specific token</span>
                </a>
                <a href="#my-served" class="action-btn" onclick="scrollToServed()">
                    <strong>📋 My Served</strong><br>
                    <span style="color: #28a745;"><?php echo $stats['my_served_tokens']; ?> today</span>
                </a>
                <a href="../auth/logout.php" class="action-btn">
                    <strong>🚪 Logout</strong><br>
                    <span style="color: #6c757d;">End session</span>
                </a>
            </div>
        </div>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-top: 2rem;">
        <!-- Pending Tokens -->
        <div class="card" id="pending-tokens">
            <div class="card-header">
                <h3>⏳ Pending Orders (<?php echo count($pending_tokens); ?>)</h3>
                <small style="color: #666;">Oldest orders first</small>
            </div>
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <?php if (empty($pending_tokens)): ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <h4>🎉 All caught up!</h4>
                        <p>No pending orders at the moment.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($pending_tokens as $token): ?>
                        <div class="token-card pending" id="token-<?php echo $token['token_id']; ?>">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 0.5rem 0;"><?php echo htmlspecialchars($token['name']); ?></h4>
                                    <p style="margin: 0; color: #666;">
                                        <strong>UID:</strong> <?php echo htmlspecialchars($token['uid']); ?><br>
                                        <strong>Phone:</strong> <?php echo htmlspecialchars($token['phone']); ?><br>
                                        <strong>Amount:</strong> <?php echo formatCurrency($token['amount']); ?><br>
                                        <strong>Ordered:</strong> <?php echo date('H:i', strtotime($token['created_at'])); ?>
                                        <small style="color: #999;">(<?php echo date('M j', strtotime($token['created_at'])); ?>)</small>
                                    </p>
                                </div>
                                <div style="text-align: right;">
                                    <button class="serve-btn" onclick="serveToken('<?php echo $token['token_id']; ?>')">
                                        ✅ Serve
                                    </button>
                                    <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #666;">
                                        <code><?php echo htmlspecialchars($token['token_id']); ?></code>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- My Served Tokens -->
        <div class="card" id="my-served">
            <div class="card-header">
                <h3>🏆 My Served Today (<?php echo count($my_served_tokens); ?>)</h3>
                <small style="color: #666;">Most recent first</small>
            </div>
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <?php if (empty($my_served_tokens)): ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <h4>🚀 Ready to start!</h4>
                        <p>You haven't served any orders today yet.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($my_served_tokens as $token): ?>
                        <div class="token-card served">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 0.5rem 0;"><?php echo htmlspecialchars($token['name']); ?></h4>
                                    <p style="margin: 0; color: #666;">
                                        <strong>UID:</strong> <?php echo htmlspecialchars($token['uid']); ?><br>
                                        <strong>Amount:</strong> <?php echo formatCurrency($token['amount']); ?><br>
                                        <strong>Served at:</strong> <?php echo date('H:i', strtotime($token['served_at'])); ?>
                                    </p>
                                </div>
                                <div style="text-align: right;">
                                    <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                        ✅ SERVED
                                    </span>
                                    <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #666;">
                                        <code><?php echo htmlspecialchars($token['token_id']); ?></code>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Server Performance -->
    <div class="card" style="margin-top: 1.5rem;">
        <div class="card-header">
            <h3>📊 Server Performance Today</h3>
        </div>
        <div class="card-body">
            <?php if (empty($server_performance)): ?>
                <p style="text-align: center; color: #666; padding: 2rem;">No performance data available.</p>
            <?php else: ?>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <?php foreach ($server_performance as $index => $server): ?>
                        <div style="text-align: center; padding: 1rem; background: <?php echo $server['full_name'] === $_SESSION['server_name'] ? '#e3f2fd' : '#f8f9fa'; ?>; border-radius: 8px; border: <?php echo $server['full_name'] === $_SESSION['server_name'] ? '2px solid #2196f3' : '1px solid #dee2e6'; ?>;">
                            <?php if ($index === 0): ?>
                                <span style="font-size: 1.5rem;">🥇</span>
                            <?php elseif ($index === 1): ?>
                                <span style="font-size: 1.5rem;">🥈</span>
                            <?php elseif ($index === 2): ?>
                                <span style="font-size: 1.5rem;">🥉</span>
                            <?php endif; ?>
                            <h4><?php echo htmlspecialchars($server['full_name']); ?></h4>
                            <?php if ($server['full_name'] === $_SESSION['server_name']): ?>
                                <p style="color: #2196f3; font-weight: bold;">(You)</p>
                            <?php endif; ?>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #007bff;">
                                <?php echo $server['tokens_served']; ?>
                            </p>
                            <p style="color: #666;">tokens served</p>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Token search functionality
let searchTimeout;
document.getElementById('tokenSearch').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();
    
    if (query.length < 3) {
        document.getElementById('searchResult').innerHTML = '';
        return;
    }
    
    searchTimeout = setTimeout(() => {
        searchToken(query);
    }, 500);
});

function searchToken(tokenId) {
    fetch('../api/token_operations.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'search_token',
            token_id: tokenId
        })
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('searchResult');
        
        if (data.success) {
            const token = data.token;
            resultDiv.innerHTML = `
                <div class="token-card ${token.status.toLowerCase()}" style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: between; align-items: center;">
                        <div style="flex: 1;">
                            <h4 style="margin: 0 0 0.5rem 0;">${token.name}</h4>
                            <p style="margin: 0; color: #666;">
                                <strong>UID:</strong> ${token.uid}<br>
                                <strong>Phone:</strong> ${token.phone}<br>
                                <strong>Amount:</strong> ₹${token.amount}<br>
                                <strong>Status:</strong> <span style="color: ${token.status === 'Pending' ? '#dc3545' : '#28a745'};">${token.status}</span>
                            </p>
                        </div>
                        <div style="text-align: right;">
                            ${token.status === 'Pending' ? 
                                `<button class="serve-btn" onclick="serveToken('${token.token_id}')">✅ Serve</button>` : 
                                `<span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">✅ ${token.status}</span>`
                            }
                        </div>
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="margin-top: 1rem; padding: 1rem; background: #f8d7da; border-radius: 5px; color: #721c24;">
                    ❌ ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('searchResult').innerHTML = `
            <div style="margin-top: 1rem; padding: 1rem; background: #f8d7da; border-radius: 5px; color: #721c24;">
                ❌ Error searching for token
            </div>
        `;
    });
}

function serveToken(tokenId) {
    if (!confirm('Are you sure you want to mark this token as served?')) {
        return;
    }
    
    fetch('../api/token_operations.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'serve_token',
            token_id: tokenId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove token from pending list
            const tokenElement = document.getElementById('token-' + tokenId);
            if (tokenElement) {
                tokenElement.style.transition = 'all 0.5s';
                tokenElement.style.opacity = '0';
                tokenElement.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    tokenElement.remove();
                }, 500);
            }
            
            // Clear search result if it matches
            const searchResult = document.getElementById('searchResult');
            if (searchResult.innerHTML.includes(tokenId)) {
                searchResult.innerHTML = `
                    <div style="margin-top: 1rem; padding: 1rem; background: #d4edda; border-radius: 5px; color: #155724;">
                        ✅ Token served successfully!
                    </div>
                `;
                setTimeout(() => {
                    searchResult.innerHTML = '';
                    document.getElementById('tokenSearch').value = '';
                }, 3000);
            }
            
            // Refresh page after a short delay to update stats
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error serving token. Please try again.');
    });
}

function scrollToPending() {
    document.getElementById('pending-tokens').scrollIntoView({ behavior: 'smooth' });
}

function scrollToServed() {
    document.getElementById('my-served').scrollIntoView({ behavior: 'smooth' });
}

// Auto-refresh every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);

// Add live clock
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    
    let clockElement = document.getElementById('server-clock');
    if (!clockElement) {
        clockElement = document.createElement('div');
        clockElement.id = 'server-clock';
        clockElement.style.cssText = 'position: fixed; top: 10px; right: 10px; background: rgba(40, 167, 69, 0.9); color: white; padding: 0.5rem; border-radius: 5px; font-family: monospace; z-index: 1000;';
        document.body.appendChild(clockElement);
    }
    
    clockElement.innerHTML = `🕐 ${timeString}`;
}

setInterval(updateClock, 1000);
updateClock();

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Alt + S to focus search
    if (e.altKey && e.key === 's') {
        e.preventDefault();
        document.getElementById('tokenSearch').focus();
    }
    
    // Alt + P to scroll to pending
    if (e.altKey && e.key === 'p') {
        e.preventDefault();
        scrollToPending();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
