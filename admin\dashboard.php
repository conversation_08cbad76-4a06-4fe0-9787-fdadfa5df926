<?php
$page_title = 'Admin Dashboard';
$show_nav = true;
require_once '../includes/header.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

try {
    $conn = getDBConnection();
    
    // Get statistics
    $stats = [];
    
    // Total tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE()
    ");
    $stmt->execute();
    $stats['today_tokens'] = $stmt->fetch()['count'];
    
    // Pending tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Pending'
    ");
    $stmt->execute();
    $stats['pending_tokens'] = $stmt->fetch()['count'];
    
    // Served tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Served'
    ");
    $stmt->execute();
    $stats['served_tokens'] = $stmt->fetch()['count'];
    
    // Total revenue today
    $stmt = $conn->prepare("
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE()
    ");
    $stmt->execute();
    $stats['today_revenue'] = $stmt->fetch()['total'];
    
    // Recent tokens
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone 
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        ORDER BY t.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent_tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading dashboard data.';
    logActivity("Admin dashboard error: " . $e->getMessage());
    $stats = ['today_tokens' => 0, 'pending_tokens' => 0, 'served_tokens' => 0, 'today_revenue' => 0];
    $recent_tokens = [];
}
?>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo $stats['today_tokens']; ?></div>
        <div class="stat-label">Total Tokens Today</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number" style="color: #ffc107;"><?php echo $stats['pending_tokens']; ?></div>
        <div class="stat-label">Pending Tokens</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number" style="color: #28a745;"><?php echo $stats['served_tokens']; ?></div>
        <div class="stat-label">Served Tokens</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number" style="color: #17a2b8;"><?php echo formatCurrency($stats['today_revenue']); ?></div>
        <div class="stat-label">Today's Revenue</div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Recent Tokens</h3>
    </div>
    <div class="card-body">
        <?php if (empty($recent_tokens)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No tokens found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Token ID</th>
                            <th>Customer</th>
                            <th>UID</th>
                            <th>Phone</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_tokens as $token): ?>
                            <tr>
                                <td>
                                    <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($token['token_id']); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($token['name']); ?></td>
                                <td><?php echo htmlspecialchars($token['uid']); ?></td>
                                <td><?php echo htmlspecialchars($token['phone']); ?></td>
                                <td><?php echo formatCurrency($token['amount']); ?></td>
                                <td><?php echo getStatusBadge($token['status']); ?></td>
                                <td><?php echo date('H:i', strtotime($token['created_at'])); ?></td>
                                <td>
                                    <?php if ($token['status'] === 'Pending'): ?>
                                        <button class="btn btn-success btn-sm serve-token-btn" 
                                                data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                            Mark Served
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-secondary btn-sm copy-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Copy
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="view_tokens.php" class="btn btn-primary">View All Tokens</a>
            <a href="statistics.php" class="btn btn-secondary">View Statistics</a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Quick Actions</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <a href="view_tokens.php?status=Pending" class="btn btn-warning">
                View Pending Tokens (<?php echo $stats['pending_tokens']; ?>)
            </a>
            <a href="view_tokens.php?date=<?php echo date('Y-m-d'); ?>" class="btn btn-info">
                Today's Tokens (<?php echo $stats['today_tokens']; ?>)
            </a>
            <a href="statistics.php" class="btn btn-success">
                View Statistics
            </a>
            <a href="../index.php" class="btn btn-secondary">
                View QR Code Page
            </a>
        </div>
    </div>
</div>

<script>
// Auto-refresh dashboard every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);

// Add notification for new tokens (you can enhance this with WebSocket or Server-Sent Events)
document.addEventListener('DOMContentLoaded', function() {
    // Check for new tokens every 10 seconds
    setInterval(checkForNewTokens, 10000);
});

function checkForNewTokens() {
    fetch('../api/token_operations.php?action=get_pending_count')
        .then(response => response.json())
        .then(data => {
            if (data.count > <?php echo $stats['pending_tokens']; ?>) {
                MessSystem.showAlert('New token(s) received!', 'info');
            }
        })
        .catch(error => console.error('Error checking for new tokens:', error));
}
</script>

<?php require_once '../includes/footer.php'; ?>
