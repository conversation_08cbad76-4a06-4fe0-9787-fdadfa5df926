<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    <meta name="description" content="Mess Management System - Digital Food Coupon System">
    <meta name="keywords" content="mess, food, coupon, digital, payment, token">
</head>
<body>
    <header class="header">
        <div class="container">
            <h1><?php echo SITE_NAME; ?></h1>
            
            <?php if (isset($show_nav) && $show_nav): ?>
            <nav class="nav">
                <ul>
                    <?php if (isAdmin()): ?>
                        <li><a href="<?php echo SITE_URL; ?>/admin/dashboard.php">Dashboard</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/view_tokens.php">View Tokens</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/admin/statistics.php">Statistics</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/auth/logout.php">Logout</a></li>
                    <?php elseif (isServer()): ?>
                        <li><a href="<?php echo SITE_URL; ?>/server/dashboard.php">Dashboard</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/server/search_token.php">Search Token</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/auth/logout.php">Logout</a></li>
                    <?php else: ?>
                        <li><a href="<?php echo SITE_URL; ?>/">Home</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/auth/admin_login.php">Admin Login</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/auth/server_login.php">Server Login</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </header>
    
    <main class="main-content">
        <div class="container">
            <?php
            // Display flash messages
            if (isset($_SESSION['success_message'])) {
                echo '<div class="alert alert-success">' . htmlspecialchars($_SESSION['success_message']) . '</div>';
                unset($_SESSION['success_message']);
            }
            
            if (isset($_SESSION['error_message'])) {
                echo '<div class="alert alert-danger">' . htmlspecialchars($_SESSION['error_message']) . '</div>';
                unset($_SESSION['error_message']);
            }
            
            if (isset($_SESSION['warning_message'])) {
                echo '<div class="alert alert-warning">' . htmlspecialchars($_SESSION['warning_message']) . '</div>';
                unset($_SESSION['warning_message']);
            }
            
            if (isset($_SESSION['info_message'])) {
                echo '<div class="alert alert-info">' . htmlspecialchars($_SESSION['info_message']) . '</div>';
                unset($_SESSION['info_message']);
            }
            ?>
