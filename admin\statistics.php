<?php
$page_title = 'Statistics';
$show_nav = true;
require_once '../includes/header.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

try {
    $conn = getDBConnection();
    
    // Get date range from query parameters
    $start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
    $end_date = $_GET['end_date'] ?? date('Y-m-d');
    
    // Daily statistics
    $stmt = $conn->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total_tokens,
            SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as pending_tokens,
            SUM(CASE WHEN status = 'Served' THEN 1 ELSE 0 END) as served_tokens,
            SUM(amount) as total_revenue
        FROM tokens 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $daily_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Overall statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_tokens,
            SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as pending_tokens,
            SUM(CASE WHEN status = 'Served' THEN 1 ELSE 0 END) as served_tokens,
            SUM(CASE WHEN status = 'Expired' THEN 1 ELSE 0 END) as expired_tokens,
            SUM(amount) as total_revenue,
            AVG(amount) as avg_amount
        FROM tokens 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    $stmt->execute([$start_date, $end_date]);
    $overall_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Top customers
    $stmt = $conn->prepare("
        SELECT 
            u.name, u.uid, u.phone,
            COUNT(t.id) as token_count,
            SUM(t.amount) as total_spent
        FROM tokens t
        JOIN users u ON t.user_id = u.id
        WHERE DATE(t.created_at) BETWEEN ? AND ?
        GROUP BY u.id
        ORDER BY token_count DESC, total_spent DESC
        LIMIT 10
    ");
    $stmt->execute([$start_date, $end_date]);
    $top_customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Server performance
    $stmt = $conn->prepare("
        SELECT 
            s.full_name,
            COUNT(t.id) as tokens_served,
            DATE(t.served_at) as date
        FROM tokens t
        JOIN server_users s ON t.served_by = s.id
        WHERE DATE(t.served_at) BETWEEN ? AND ?
        GROUP BY s.id, DATE(t.served_at)
        ORDER BY date DESC, tokens_served DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $server_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading statistics.';
    logActivity("Admin statistics error: " . $e->getMessage());
    $daily_stats = [];
    $overall_stats = ['total_tokens' => 0, 'pending_tokens' => 0, 'served_tokens' => 0, 'expired_tokens' => 0, 'total_revenue' => 0, 'avg_amount' => 0];
    $top_customers = [];
    $server_performance = [];
}
?>

<div class="card">
    <div class="card-header">
        <h3>Statistics</h3>
    </div>
    <div class="card-body">
        <!-- Date Range Filter -->
        <form method="GET" style="margin-bottom: 2rem;">
            <div style="display: flex; gap: 1rem; align-items: end; flex-wrap: wrap;">
                <div>
                    <label class="form-label">Start Date</label>
                    <input type="date" name="start_date" class="form-control" value="<?php echo htmlspecialchars($start_date); ?>">
                </div>
                <div>
                    <label class="form-label">End Date</label>
                    <input type="date" name="end_date" class="form-control" value="<?php echo htmlspecialchars($end_date); ?>">
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
                <div>
                    <a href="?start_date=<?php echo date('Y-m-d'); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-secondary">Today</a>
                </div>
                <div>
                    <a href="?start_date=<?php echo date('Y-m-d', strtotime('-7 days')); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-secondary">Last 7 Days</a>
                </div>
            </div>
        </form>
        
        <!-- Overall Statistics -->
        <div class="stats-grid" style="margin-bottom: 2rem;">
            <div class="stat-card">
                <div class="stat-number"><?php echo $overall_stats['total_tokens']; ?></div>
                <div class="stat-label">Total Tokens</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" style="color: #ffc107;"><?php echo $overall_stats['pending_tokens']; ?></div>
                <div class="stat-label">Pending</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745;"><?php echo $overall_stats['served_tokens']; ?></div>
                <div class="stat-label">Served</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number" style="color: #17a2b8;"><?php echo formatCurrency($overall_stats['total_revenue']); ?></div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>
    </div>
</div>

<!-- Daily Statistics -->
<div class="card">
    <div class="card-header">
        <h3>Daily Breakdown</h3>
    </div>
    <div class="card-body">
        <?php if (empty($daily_stats)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No data found for the selected date range.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Total Tokens</th>
                            <th>Pending</th>
                            <th>Served</th>
                            <th>Revenue</th>
                            <th>Completion Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($daily_stats as $stat): ?>
                            <?php 
                            $completion_rate = $stat['total_tokens'] > 0 ? 
                                round(($stat['served_tokens'] / $stat['total_tokens']) * 100, 1) : 0;
                            ?>
                            <tr>
                                <td><?php echo date('M j, Y', strtotime($stat['date'])); ?></td>
                                <td><?php echo $stat['total_tokens']; ?></td>
                                <td><span class="badge badge-warning"><?php echo $stat['pending_tokens']; ?></span></td>
                                <td><span class="badge badge-success"><?php echo $stat['served_tokens']; ?></span></td>
                                <td><?php echo formatCurrency($stat['total_revenue']); ?></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <div style="background: #e9ecef; height: 8px; border-radius: 4px; flex: 1; overflow: hidden;">
                                            <div style="background: #28a745; height: 100%; width: <?php echo $completion_rate; ?>%;"></div>
                                        </div>
                                        <span style="font-size: 0.8rem;"><?php echo $completion_rate; ?>%</span>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Top Customers -->
<div class="card">
    <div class="card-header">
        <h3>Top Customers</h3>
    </div>
    <div class="card-body">
        <?php if (empty($top_customers)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No customer data found for the selected date range.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Name</th>
                            <th>UID</th>
                            <th>Phone</th>
                            <th>Orders</th>
                            <th>Total Spent</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_customers as $index => $customer): ?>
                            <tr>
                                <td>
                                    <?php 
                                    $rank = $index + 1;
                                    $medal = '';
                                    if ($rank === 1) $medal = '🥇';
                                    elseif ($rank === 2) $medal = '🥈';
                                    elseif ($rank === 3) $medal = '🥉';
                                    echo $medal . ' ' . $rank;
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($customer['name']); ?></td>
                                <td><?php echo htmlspecialchars($customer['uid']); ?></td>
                                <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                <td><span class="badge badge-primary"><?php echo $customer['token_count']; ?></span></td>
                                <td><?php echo formatCurrency($customer['total_spent']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Server Performance -->
<?php if (!empty($server_performance)): ?>
<div class="card">
    <div class="card-header">
        <h3>Server Performance</h3>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Server</th>
                        <th>Date</th>
                        <th>Tokens Served</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($server_performance as $performance): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($performance['full_name']); ?></td>
                            <td><?php echo date('M j, Y', strtotime($performance['date'])); ?></td>
                            <td><span class="badge badge-success"><?php echo $performance['tokens_served']; ?></span></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<div style="text-align: center; margin-top: 2rem;">
    <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
    <button onclick="window.print()" class="btn btn-secondary">Print Report</button>
</div>

<style>
@media print {
    .btn, .nav, .form-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 1rem;
        page-break-inside: avoid;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
