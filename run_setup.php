<?php
// Simple script to run database setup
require_once 'config/config.php';

echo "Starting database setup...\n";

try {
    // Read SQL file
    $sql_file = __DIR__ . '/database/schema.sql';

    if (!file_exists($sql_file)) {
        throw new Exception("SQL schema file not found: $sql_file");
    }

    $sql_content = file_get_contents($sql_file);

    if ($sql_content === false) {
        throw new Exception("Could not read SQL schema file");
    }

    // Connect to MySQL server (without database)
    $host = 'localhost';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected to MySQL server successfully.\n";

    // Disable foreign key checks during setup
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "Disabled foreign key checks for setup.\n";

    // Split SQL into individual statements (handle multi-line statements)
    $statements = [];
    $current_statement = '';
    $lines = explode("\n", $sql_content);

    foreach ($lines as $line) {
        $line = trim($line);

        // Skip empty lines and comments
        if (empty($line) || preg_match('/^\s*--/', $line)) {
            continue;
        }

        $current_statement .= $line . ' ';

        // If line ends with semicolon, it's the end of a statement
        if (substr($line, -1) === ';') {
            $statements[] = trim($current_statement);
            $current_statement = '';
        }
    }

    // Add any remaining statement
    if (!empty(trim($current_statement))) {
        $statements[] = trim($current_statement);
    }

    // Filter out empty statements
    $statements = array_filter($statements, function($stmt) {
        return !empty(trim($stmt));
    });

    echo "Found " . count($statements) . " SQL statements to execute.\n";

    // Execute each statement
    foreach ($statements as $index => $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Statement " . ($index + 1) . " executed successfully\n";
            } catch (PDOException $e) {
                // Skip if database already exists
                if (strpos($e->getMessage(), 'database exists') !== false) {
                    echo "⚠ Database already exists, skipping creation\n";
                } else {
                    echo "✗ Error in statement " . ($index + 1) . ": " . $e->getMessage() . "\n";
                }
            }
        }
    }

    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "Re-enabled foreign key checks.\n";

    echo "\nSetup completed successfully!\n";
    echo "Your Mess Management System is now ready to use.\n";

} catch (Exception $e) {
    echo "Setup Failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
}
?>
