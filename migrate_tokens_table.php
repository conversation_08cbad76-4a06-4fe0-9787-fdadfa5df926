<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>Database Migration: Tokens Table</h1>";

try {
    $conn = getDBConnection();
    echo "✅ Database connection successful<br>";
    
    // Check current table structure
    echo "<h2>Current Table Structure</h2>";
    $stmt = $conn->prepare("DESCRIBE tokens");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
    }
    
    // Check if served_by and served_at columns exist
    $needs_served_by = !in_array('served_by', $existing_columns);
    $needs_served_at = !in_array('served_at', $existing_columns);
    
    if (!$needs_served_by && !$needs_served_at) {
        echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ All required columns already exist. No migration needed.";
        echo "</div>";
    } else {
        echo "<h2>Migration Required</h2>";
        
        if ($needs_served_by) {
            echo "Adding served_by column...<br>";
            try {
                $conn->exec("ALTER TABLE tokens ADD COLUMN served_by INT NULL");
                echo "✅ Added served_by column<br>";
            } catch (Exception $e) {
                echo "❌ Error adding served_by column: " . $e->getMessage() . "<br>";
            }
        }
        
        if ($needs_served_at) {
            echo "Adding served_at column...<br>";
            try {
                $conn->exec("ALTER TABLE tokens ADD COLUMN served_at TIMESTAMP NULL");
                echo "✅ Added served_at column<br>";
            } catch (Exception $e) {
                echo "❌ Error adding served_at column: " . $e->getMessage() . "<br>";
            }
        }
        
        // Add foreign key constraint for served_by if it doesn't exist
        echo "Adding foreign key constraint...<br>";
        try {
            // First check if the constraint already exists
            $stmt = $conn->prepare("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'mess_management' 
                AND TABLE_NAME = 'tokens' 
                AND COLUMN_NAME = 'served_by' 
                AND REFERENCED_TABLE_NAME = 'server_users'
            ");
            $stmt->execute();
            $constraint_exists = $stmt->fetch();
            
            if (!$constraint_exists) {
                $conn->exec("ALTER TABLE tokens ADD FOREIGN KEY (served_by) REFERENCES server_users(id) ON DELETE SET NULL");
                echo "✅ Added foreign key constraint for served_by<br>";
            } else {
                echo "ℹ️ Foreign key constraint already exists<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error adding foreign key constraint: " . $e->getMessage() . "<br>";
        }
        
        echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Migration completed successfully!";
        echo "</div>";
    }
    
    // Show final table structure
    echo "<h2>Final Table Structure</h2>";
    $stmt = $conn->prepare("DESCRIBE tokens");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<p><a href='debug_serve_token.php'>Run Debug Script</a> | <a href='index.php'>Go to Home Page</a></p>";
?>
