<?php
/**
 * Database Setup Helper
 * Provides utility functions for database operations
 */

require_once __DIR__ . '/../config/config.php';

class DatabaseSetupHelper {
    private $conn;
    
    public function __construct() {
        $this->conn = getDBConnection();
    }
    
    /**
     * Check if database setup is complete
     */
    public function isSetupComplete() {
        try {
            // Check if all required tables exist
            $required_tables = ['users', 'admin_users', 'server_users', 'tokens', 'payments'];
            
            foreach ($required_tables as $table) {
                $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if (!$stmt->fetch()) {
                    return false;
                }
            }
            
            // Check if default admin exists
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM admin_users");
            $stmt->execute();
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get database statistics
     */
    public function getDatabaseStats() {
        try {
            $stats = [];
            
            // Table counts
            $tables = ['users', 'admin_users', 'server_users', 'tokens', 'payments'];
            foreach ($tables as $table) {
                $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM $table");
                $stmt->execute();
                $result = $stmt->fetch();
                $stats[$table] = $result['count'];
            }
            
            // Database size
            $stmt = $this->conn->prepare("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = 'mess_management'
            ");
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['database_size_mb'] = $result['size_mb'] ?? 0;
            
            return $stats;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Create default admin user
     */
    public function createDefaultAdmin($username = 'admin', $password = 'admin123', $email = '<EMAIL>') {
        try {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $this->conn->prepare("
                INSERT IGNORE INTO admin_users (username, password, email) 
                VALUES (?, ?, ?)
            ");
            
            return $stmt->execute([$username, $hashed_password, $email]);
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Create default server user
     */
    public function createDefaultServer($username = 'server1', $password = 'server123', $full_name = 'Server Staff 1') {
        try {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $this->conn->prepare("
                INSERT IGNORE INTO server_users (username, password, full_name) 
                VALUES (?, ?, ?)
            ");
            
            return $stmt->execute([$username, $hashed_password, $full_name]);
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Clean up old expired tokens
     */
    public function cleanupExpiredTokens($days_old = 30) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE tokens 
                SET status = 'Expired' 
                WHERE status = 'Pending' 
                AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            $stmt->execute([$days_old]);
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Backup database to SQL file
     */
    public function backupDatabase($backup_path = null) {
        if (!$backup_path) {
            $backup_path = __DIR__ . '/backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        // Create backups directory if it doesn't exist
        $backup_dir = dirname($backup_path);
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        try {
            $tables = ['users', 'admin_users', 'server_users', 'tokens', 'payments'];
            $sql_dump = "-- Mess Management System Database Backup\n";
            $sql_dump .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($tables as $table) {
                // Get table structure
                $stmt = $this->conn->prepare("SHOW CREATE TABLE $table");
                $stmt->execute();
                $create_table = $stmt->fetch();
                
                $sql_dump .= "-- Table structure for $table\n";
                $sql_dump .= "DROP TABLE IF EXISTS `$table`;\n";
                $sql_dump .= $create_table['Create Table'] . ";\n\n";
                
                // Get table data
                $stmt = $this->conn->prepare("SELECT * FROM $table");
                $stmt->execute();
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($rows)) {
                    $sql_dump .= "-- Data for table $table\n";
                    
                    foreach ($rows as $row) {
                        $values = array_map(function($value) {
                            return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                        }, array_values($row));
                        
                        $sql_dump .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $sql_dump .= "\n";
                }
            }
            
            return file_put_contents($backup_path, $sql_dump) !== false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get system health check
     */
    public function getSystemHealth() {
        $health = [
            'database_connection' => false,
            'tables_exist' => false,
            'admin_exists' => false,
            'server_exists' => false,
            'write_permissions' => false,
            'php_version' => PHP_VERSION,
            'mysql_version' => '',
            'issues' => []
        ];
        
        try {
            // Database connection
            $health['database_connection'] = $this->conn !== null;
            
            // MySQL version
            $stmt = $this->conn->prepare("SELECT VERSION() as version");
            $stmt->execute();
            $result = $stmt->fetch();
            $health['mysql_version'] = $result['version'];
            
            // Tables exist
            $health['tables_exist'] = $this->isSetupComplete();
            
            // Admin exists
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM admin_users");
            $stmt->execute();
            $result = $stmt->fetch();
            $health['admin_exists'] = $result['count'] > 0;
            
            // Server exists
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM server_users");
            $stmt->execute();
            $result = $stmt->fetch();
            $health['server_exists'] = $result['count'] > 0;
            
            // Write permissions
            $test_file = __DIR__ . '/test_write.tmp';
            $health['write_permissions'] = file_put_contents($test_file, 'test') !== false;
            if (file_exists($test_file)) {
                unlink($test_file);
            }
            
            // Check for issues
            if (!$health['database_connection']) {
                $health['issues'][] = 'Database connection failed';
            }
            if (!$health['tables_exist']) {
                $health['issues'][] = 'Database tables are missing';
            }
            if (!$health['admin_exists']) {
                $health['issues'][] = 'No admin users found';
            }
            if (!$health['server_exists']) {
                $health['issues'][] = 'No server users found';
            }
            if (!$health['write_permissions']) {
                $health['issues'][] = 'Write permissions not available';
            }
            if (version_compare(PHP_VERSION, '7.4.0', '<')) {
                $health['issues'][] = 'PHP version should be 7.4 or higher';
            }
            
        } catch (Exception $e) {
            $health['issues'][] = 'Health check failed: ' . $e->getMessage();
        }
        
        return $health;
    }
    
    /**
     * Reset database to initial state
     */
    public function resetDatabase() {
        try {
            // Disable foreign key checks
            $this->conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Truncate all tables
            $tables = ['tokens', 'payments', 'users', 'admin_users', 'server_users'];
            foreach ($tables as $table) {
                $this->conn->exec("TRUNCATE TABLE $table");
            }
            
            // Re-enable foreign key checks
            $this->conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            // Create default users
            $this->createDefaultAdmin();
            $this->createDefaultServer();
            
            return true;
            
        } catch (Exception $e) {
            return false;
        }
    }
}

// Usage example (if called directly)
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    header('Content-Type: application/json');
    
    $action = $_GET['action'] ?? 'health';
    $helper = new DatabaseSetupHelper();
    
    switch ($action) {
        case 'health':
            echo json_encode($helper->getSystemHealth());
            break;
            
        case 'stats':
            echo json_encode($helper->getDatabaseStats());
            break;
            
        case 'cleanup':
            $days = $_GET['days'] ?? 30;
            $cleaned = $helper->cleanupExpiredTokens($days);
            echo json_encode(['cleaned_tokens' => $cleaned]);
            break;
            
        case 'backup':
            $success = $helper->backupDatabase();
            echo json_encode(['backup_success' => $success]);
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
}
?>
