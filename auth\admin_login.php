<?php
$page_title = 'Admin Login';
$show_nav = true;
require_once '../includes/header.php';

// Redirect if already logged in
if (isAdmin()) {
    redirect('../admin/dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        try {
            $conn = getDBConnection();
            $stmt = $conn->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin && password_verify($password, $admin['password'])) {
                // Login successful
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                
                // Update last login
                $stmt = $conn->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);
                
                logActivity("Admin login successful", 'admin', $admin['id']);
                
                $_SESSION['success_message'] = 'Welcome back, ' . $admin['username'] . '!';
                redirect('../admin/dashboard.php');
            } else {
                $error = 'Invalid username or password.';
                logActivity("Failed admin login attempt for username: $username", 'admin');
            }
        } catch (Exception $e) {
            $error = 'Login failed. Please try again.';
            logActivity("Admin login error: " . $e->getMessage());
        }
    }
}
?>

<div class="card" style="max-width: 400px; margin: 2rem auto;">
    <div class="card-header">
        <h2>Admin Login</h2>
    </div>
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <form method="POST" data-validate="true">
            <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="form-control" 
                    required 
                    value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                    placeholder="Enter admin username"
                    autocomplete="username"
                >
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-control" 
                    required 
                    placeholder="Enter password"
                    autocomplete="current-password"
                >
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    Login
                </button>
            </div>
        </form>
        
        <div style="text-align: center; margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 0.9rem;">
                Default credentials:<br>
                Username: <strong>admin</strong><br>
                Password: <strong>admin123</strong>
            </p>
        </div>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="../index.php" class="btn btn-secondary">Back to Home</a>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
