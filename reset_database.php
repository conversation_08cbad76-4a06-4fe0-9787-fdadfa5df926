<?php
// Database Reset Script for Mess Management System
// This script will drop all tables and recreate them

require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Reset Database - Mess Management System</title>
    <link rel='stylesheet' href='assets/css/style.css'>
</head>
<body>
    <div class='container' style='max-width: 800px; margin: 2rem auto;'>
        <div class='card'>
            <div class='card-header'>
                <h2>🔄 Reset Database - Mess Management System</h2>
            </div>
            <div class='card-body'>";

try {
    // Connect to MySQL server
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>Connected to MySQL server successfully.</div>";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<div class='alert alert-info'>Disabled foreign key checks.</div>";
    
    // Use the database
    $pdo->exec("USE mess_management");
    echo "<div class='alert alert-info'>Using mess_management database.</div>";
    
    // Drop all tables
    $tables = ['tokens', 'payments', 'users', 'admin_users', 'server_users'];
    foreach ($tables as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS $table");
            echo "<div style='color: orange; margin: 0.5rem 0;'>🗑️ Dropped table: $table</div>";
        } catch (PDOException $e) {
            echo "<div style='color: red; margin: 0.5rem 0;'>❌ Error dropping table $table: " . $e->getMessage() . "</div>";
        }
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<div class='alert alert-info'>Re-enabled foreign key checks.</div>";
    
    echo "<div class='alert alert-success'>
            <h4>Database Reset Complete!</h4>
            <p>All tables have been dropped. You can now run the setup script again.</p>
        </div>";
    
    echo "<div style='text-align: center; margin-top: 2rem;'>
            <a href='setup.php' class='btn btn-primary'>Run Setup Script</a>
            <a href='index.php' class='btn btn-secondary'>Go to Home Page</a>
        </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h4>Reset Failed!</h4>
            <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>Please check your database configuration and try again.</p>
        </div>";
    
    echo "<div style='background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545; margin: 2rem 0;'>
            <h4>Troubleshooting:</h4>
            <ul style='margin: 0; padding-left: 1.5rem;'>
                <li>Make sure XAMPP/MySQL is running</li>
                <li>Check database credentials in <code>config/database.php</code></li>
                <li>Ensure the mess_management database exists</li>
                <li>Verify file permissions</li>
            </ul>
        </div>";
}

echo "            </div>
        </div>
    </div>
</body>
</html>";
?>
