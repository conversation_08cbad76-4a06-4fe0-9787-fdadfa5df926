<?php
header('Content-Type: application/json');
require_once '../config/config.php';
require_once '../includes/functions.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Check if order data exists in session
if (!isset($_SESSION['order_data'])) {
    http_response_code(400);
    echo json_encode(['error' => 'No order data found']);
    exit;
}

$order_data = $_SESSION['order_data'];

try {
    // Create Razorpay order
    $amount = $order_data['amount'] * 100; // Convert to paise
    $currency = 'INR';
    
    // Generate unique receipt ID
    $receipt = 'receipt_' . time() . '_' . $order_data['user_id'];
    
    // Prepare order data for Razorpay
    $razorpay_order_data = [
        'amount' => $amount,
        'currency' => $currency,
        'receipt' => $receipt,
        'notes' => [
            'user_id' => $order_data['user_id'],
            'name' => $order_data['name'],
            'phone' => $order_data['phone']
        ]
    ];
    
    // Create order using Razorpay API
    $razorpay_order = createRazorpayOrder($razorpay_order_data);
    
    if (!$razorpay_order) {
        throw new Exception('Failed to create Razorpay order');
    }
    
    // Store order details in database
    $conn = getDBConnection();
    
    // Generate payment ID
    $payment_id = 'PAY_' . generateUUID();
    
    // Insert payment record with Razorpay order ID
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_id, user_id, amount, status, razorpay_order_id) 
        VALUES (?, ?, ?, 'created', ?)
    ");
    $stmt->execute([
        $payment_id, 
        $order_data['user_id'], 
        $order_data['amount'], 
        $razorpay_order['id']
    ]);
    
    // Store payment ID in session
    $_SESSION['payment_id'] = $payment_id;
    
    // Return order details for frontend
    echo json_encode([
        'success' => true,
        'order_id' => $razorpay_order['id'],
        'amount' => $amount,
        'currency' => $currency,
        'key' => RAZORPAY_KEY_ID,
        'payment_id' => $payment_id,
        'prefill' => [
            'name' => $order_data['name'],
            'email' => $order_data['email'] ?? '',
            'contact' => $order_data['phone']
        ]
    ]);
    
} catch (Exception $e) {
    error_log('Razorpay order creation failed: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create payment order']);
}

/**
 * Create Razorpay order using cURL
 */
function createRazorpayOrder($order_data) {
    $url = 'https://api.razorpay.com/v1/orders';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    error_log('Razorpay API Error: ' . $response);
    return false;
}
?>
