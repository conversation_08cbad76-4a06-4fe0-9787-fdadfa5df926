<?php
$page_title = 'View Tokens';
$show_nav = true;
require_once '../includes/header.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_filter = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';

try {
    $conn = getDBConnection();
    
    // Build query with filters
    $where_conditions = [];
    $params = [];
    
    if ($status_filter) {
        $where_conditions[] = "t.status = ?";
        $params[] = $status_filter;
    }
    
    if ($date_filter) {
        $where_conditions[] = "DATE(t.created_at) = ?";
        $params[] = $date_filter;
    }
    
    if ($search) {
        $where_conditions[] = "(t.token_id LIKE ? OR u.name LIKE ? OR u.uid LIKE ? OR u.phone LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    // Get tokens with pagination
    $page = $_GET['page'] ?? 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    $query = "
        SELECT t.*, u.name, u.uid, u.phone, s.full_name as served_by_name
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        LEFT JOIN server_users s ON t.served_by = s.id
        $where_clause
        ORDER BY t.created_at DESC 
        LIMIT $per_page OFFSET $offset
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count for pagination
    $count_query = "
        SELECT COUNT(*) as total 
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        $where_clause
    ";
    $stmt = $conn->prepare($count_query);
    $stmt->execute($params);
    $total_tokens = $stmt->fetch()['total'];
    $total_pages = ceil($total_tokens / $per_page);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading tokens.';
    logActivity("Admin view tokens error: " . $e->getMessage());
    $tokens = [];
    $total_tokens = 0;
    $total_pages = 0;
}
?>

<div class="card">
    <div class="card-header">
        <h3>All Tokens</h3>
    </div>
    <div class="card-body">
        <!-- Filters -->
        <form method="GET" style="margin-bottom: 2rem;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <div>
                    <label class="form-label">Status</label>
                    <select name="status" class="form-control">
                        <option value="">All Statuses</option>
                        <option value="Pending" <?php echo $status_filter === 'Pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="Served" <?php echo $status_filter === 'Served' ? 'selected' : ''; ?>>Served</option>
                        <option value="Expired" <?php echo $status_filter === 'Expired' ? 'selected' : ''; ?>>Expired</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">Date</label>
                    <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($date_filter); ?>">
                </div>
                
                <div>
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="Token ID, Name, UID, Phone" 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div style="display: flex; align-items: end; gap: 0.5rem;">
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="view_tokens.php" class="btn btn-secondary">Clear</a>
                </div>
            </div>
        </form>
        
        <!-- Results summary -->
        <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
            <strong>Showing <?php echo count($tokens); ?> of <?php echo $total_tokens; ?> tokens</strong>
            <?php if ($status_filter || $date_filter || $search): ?>
                <span style="color: #666;">(filtered)</span>
            <?php endif; ?>
        </div>
        
        <?php if (empty($tokens)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No tokens found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Token ID</th>
                            <th>Customer</th>
                            <th>UID</th>
                            <th>Phone</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Served By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tokens as $token): ?>
                            <tr>
                                <td>
                                    <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($token['token_id']); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($token['name']); ?></td>
                                <td><?php echo htmlspecialchars($token['uid']); ?></td>
                                <td><?php echo htmlspecialchars($token['phone']); ?></td>
                                <td><?php echo formatCurrency($token['amount']); ?></td>
                                <td><?php echo getStatusBadge($token['status']); ?></td>
                                <td>
                                    <?php echo date('M j, Y H:i', strtotime($token['created_at'])); ?>
                                </td>
                                <td>
                                    <?php 
                                    if ($token['served_by_name']) {
                                        echo htmlspecialchars($token['served_by_name']);
                                        echo '<br><small>' . date('M j, H:i', strtotime($token['served_at'])) . '</small>';
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php if ($token['status'] === 'Pending'): ?>
                                        <button class="btn btn-success btn-sm serve-token-btn" 
                                                data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                            Mark Served
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-secondary btn-sm copy-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Copy
                                    </button>
                                    <button class="btn btn-danger btn-sm delete-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div style="text-align: center; margin-top: 2rem;">
                    <div style="display: inline-flex; gap: 0.5rem;">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                               class="btn btn-secondary">Previous</a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                               class="btn <?php echo $i === (int)$page ? 'btn-primary' : 'btn-secondary'; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                               class="btn btn-secondary">Next</a>
                        <?php endif; ?>
                    </div>
                    <p style="margin-top: 1rem; color: #666;">
                        Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                    </p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
