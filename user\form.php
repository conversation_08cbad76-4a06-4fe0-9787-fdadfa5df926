<?php
$page_title = 'Order Food';
$show_nav = true;
require_once '../includes/header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name']);
    $uid = sanitizeInput($_POST['uid']);
    $phone = sanitizeInput($_POST['phone']);
    $email = sanitizeInput($_POST['email']);
    
    $errors = [];
    
    // Validation
    if (empty($name)) {
        $errors[] = 'Name is required';
    }
    
    if (empty($uid)) {
        $errors[] = 'UID is required';
    } elseif (!validateUID($uid)) {
        $errors[] = 'UID must be 6-20 characters (letters and numbers only)';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required';
    } elseif (!validatePhone($phone)) {
        $errors[] = 'Please enter a valid 10-digit phone number';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($errors)) {
        try {
            $conn = getDBConnection();
            
            // Check if user already exists with same UID or phone
            $stmt = $conn->prepare("SELECT id FROM users WHERE uid = ? OR phone = ?");
            $stmt->execute([$uid, $phone]);
            $existing_user = $stmt->fetch();
            
            if ($existing_user) {
                $user_id = $existing_user['id'];
            } else {
                // Insert new user
                $stmt = $conn->prepare("INSERT INTO users (name, uid, phone, email) VALUES (?, ?, ?, ?)");
                $stmt->execute([$name, $uid, $phone, $email]);
                $user_id = $conn->lastInsertId();
            }
            
            // Store user data in session for payment
            $_SESSION['order_data'] = [
                'user_id' => $user_id,
                'name' => $name,
                'uid' => $uid,
                'phone' => $phone,
                'email' => $email,
                'amount' => MEAL_PRICE
            ];
            
            // Redirect to payment
            redirect('../user/payment.php');
            
        } catch (Exception $e) {
            $errors[] = 'An error occurred. Please try again.';
            logActivity("Form submission error: " . $e->getMessage());
        }
    }
}
?>

<div class="card">
    <div class="card-header">
        <h2>Order Your Meal</h2>
    </div>
    <div class="card-body">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul style="margin: 0; padding-left: 1.5rem;">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" data-validate="true">
            <div class="form-group">
                <label for="name" class="form-label">Full Name *</label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    class="form-control" 
                    required 
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                    placeholder="Enter your full name"
                >
            </div>
            
            <div class="form-group">
                <label for="uid" class="form-label">Student/Employee UID *</label>
                <input 
                    type="text" 
                    id="uid" 
                    name="uid" 
                    class="form-control" 
                    required 
                    data-validate-type="uid"
                    value="<?php echo isset($_POST['uid']) ? htmlspecialchars($_POST['uid']) : ''; ?>"
                    placeholder="Enter your UID (6-20 characters)"
                >
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">Phone Number *</label>
                <input 
                    type="tel" 
                    id="phone" 
                    name="phone" 
                    class="form-control" 
                    required 
                    data-validate-type="phone"
                    value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>"
                    placeholder="Enter 10-digit phone number"
                    maxlength="10"
                >
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address (Optional)</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-control" 
                    data-validate-type="email"
                    value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                    placeholder="Enter your email address"
                >
            </div>
            
            <div class="form-group">
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; border-left: 4px solid #667eea;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #667eea;">Order Summary</h4>
                    <p style="margin: 0; font-size: 1.1rem;">
                        <strong>Meal Price: <?php echo formatCurrency(MEAL_PRICE); ?></strong>
                    </p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: #666;">
                        Payment will be processed securely through Razorpay
                    </p>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary" style="width: 100%; font-size: 1.1rem;">
                    Proceed to Payment
                </button>
            </div>
        </form>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="../index.php" class="btn btn-secondary">Back to Home</a>
        </div>
    </div>
</div>

<script>
// Auto-format phone number
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 10) {
        value = value.slice(0, 10);
    }
    e.target.value = value;
});

// Auto-format UID
document.getElementById('uid').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^A-Za-z0-9]/g, '');
    if (value.length > 20) {
        value = value.slice(0, 20);
    }
    e.target.value = value;
});
</script>

<?php require_once '../includes/footer.php'; ?>
