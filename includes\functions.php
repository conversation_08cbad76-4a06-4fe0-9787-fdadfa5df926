<?php
// Common functions

// Generate unique token
function generateToken() {
    return 'MESS-' . strtoupper(uniqid()) . '-' . date('Ymd');
}

// Generate UUID
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

// Sanitize input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Validate phone number
function validatePhone($phone) {
    return preg_match('/^[6-9]\d{9}$/', $phone);
}

// Validate UID
function validateUID($uid) {
    return preg_match('/^[A-Za-z0-9]{6,20}$/', $uid);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

// Check if user is server staff
function isServer() {
    return isset($_SESSION['server_id']) && !empty($_SESSION['server_id']);
}

// Redirect function
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Format currency
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

// Get token status badge
function getStatusBadge($status) {
    switch($status) {
        case 'Pending':
            return '<span class="badge badge-warning">Pending</span>';
        case 'Served':
            return '<span class="badge badge-success">Served</span>';
        case 'Expired':
            return '<span class="badge badge-danger">Expired</span>';
        default:
            return '<span class="badge badge-secondary">Unknown</span>';
    }
}

// Log activity
function logActivity($message, $user_type = 'system', $user_id = null) {
    $log_file = __DIR__ . '/../logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$user_type:$user_id] $message" . PHP_EOL;

    // Create logs directory if it doesn't exist
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Send JSON response
function sendJsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

// Verify Razorpay signature
function verifyRazorpaySignature($razorpay_order_id, $razorpay_payment_id, $razorpay_signature) {
    $generated_signature = hash_hmac('sha256', $razorpay_order_id . "|" . $razorpay_payment_id, RAZORPAY_KEY_SECRET);
    return hash_equals($generated_signature, $razorpay_signature);
}

// Get user by ID
function getUserById($user_id) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get token by token_id
function getTokenByTokenId($token_id) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone
        FROM tokens t
        JOIN users u ON t.user_id = u.id
        WHERE t.token_id = ?
    ");
    $stmt->execute([$token_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Update token status
function updateTokenStatus($token_id, $status, $served_by = null) {
    $conn = getDBConnection();

    try {
        if ($status === 'Served') {
            // Validate served_by if provided
            if ($served_by !== null) {
                $stmt = $conn->prepare("SELECT id FROM server_users WHERE id = ?");
                $stmt->execute([$served_by]);
                if (!$stmt->fetch()) {
                    // If server_by ID doesn't exist, set it to null
                    $served_by = null;
                }
            }

            $stmt = $conn->prepare("
                UPDATE tokens
                SET status = ?, served_at = NOW(), served_by = ?
                WHERE token_id = ?
            ");
            return $stmt->execute([$status, $served_by, $token_id]);
        } else {
            $stmt = $conn->prepare("UPDATE tokens SET status = ? WHERE token_id = ?");
            return $stmt->execute([$status, $token_id]);
        }
    } catch (Exception $e) {
        error_log("Error updating token status: " . $e->getMessage());
        return false;
    }
}
?>
