<?php
header('Content-Type: application/json');
require_once '../config/config.php';
require_once '../includes/functions.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Check if it's a webhook or payment verification
    if (isset($input['event'])) {
        // Handle Razorpay webhook
        handleWebhook($input);
    } else {
        // Handle payment verification from frontend
        handlePaymentVerification();
    }
    
} catch (Exception $e) {
    error_log('Payment callback error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Handle payment verification from frontend
 */
function handlePaymentVerification() {
    $razorpay_payment_id = $_POST['razorpay_payment_id'] ?? '';
    $razorpay_order_id = $_POST['razorpay_order_id'] ?? '';
    $razorpay_signature = $_POST['razorpay_signature'] ?? '';
    $payment_id = $_POST['payment_id'] ?? '';
    
    if (empty($razorpay_payment_id) || empty($razorpay_order_id) || empty($razorpay_signature) || empty($payment_id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        return;
    }
    
    // Verify signature
    if (!verifyRazorpaySignature($razorpay_order_id, $razorpay_payment_id, $razorpay_signature)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid signature']);
        return;
    }
    
    // Get payment details from Razorpay
    $payment_details = getRazorpayPayment($razorpay_payment_id);
    
    if (!$payment_details || $payment_details['status'] !== 'captured') {
        http_response_code(400);
        echo json_encode(['error' => 'Payment not captured']);
        return;
    }
    
    // Update payment in database
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        UPDATE payments 
        SET status = 'captured', 
            razorpay_payment_id = ?, 
            razorpay_signature = ?,
            updated_at = NOW()
        WHERE payment_id = ? AND razorpay_order_id = ?
    ");
    
    $success = $stmt->execute([
        $razorpay_payment_id,
        $razorpay_signature,
        $payment_id,
        $razorpay_order_id
    ]);
    
    if ($success) {
        // Generate token
        generateTokenForPayment($payment_id);
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment verified successfully',
            'redirect_url' => '../user/payment_success.php?payment_id=' . $payment_id
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update payment']);
    }
}

/**
 * Handle Razorpay webhooks
 */
function handleWebhook($data) {
    // Verify webhook signature (optional but recommended)
    $webhook_signature = $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] ?? '';
    $webhook_secret = ''; // Set your webhook secret if configured
    
    if (!empty($webhook_secret)) {
        $expected_signature = hash_hmac('sha256', file_get_contents('php://input'), $webhook_secret);
        if (!hash_equals($expected_signature, $webhook_signature)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid webhook signature']);
            return;
        }
    }
    
    $event = $data['event'];
    $payment_entity = $data['payload']['payment']['entity'] ?? null;
    
    if ($event === 'payment.captured' && $payment_entity) {
        handlePaymentCaptured($payment_entity);
    } elseif ($event === 'payment.failed' && $payment_entity) {
        handlePaymentFailed($payment_entity);
    }
    
    echo json_encode(['status' => 'ok']);
}

/**
 * Handle payment captured webhook
 */
function handlePaymentCaptured($payment_entity) {
    $conn = getDBConnection();
    
    $razorpay_payment_id = $payment_entity['id'];
    $razorpay_order_id = $payment_entity['order_id'];
    
    // Update payment status
    $stmt = $conn->prepare("
        UPDATE payments 
        SET status = 'captured', 
            razorpay_payment_id = ?,
            updated_at = NOW()
        WHERE razorpay_order_id = ?
    ");
    
    $stmt->execute([$razorpay_payment_id, $razorpay_order_id]);
    
    // Get payment ID
    $stmt = $conn->prepare("SELECT payment_id FROM payments WHERE razorpay_order_id = ?");
    $stmt->execute([$razorpay_order_id]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($payment) {
        generateTokenForPayment($payment['payment_id']);
    }
}

/**
 * Handle payment failed webhook
 */
function handlePaymentFailed($payment_entity) {
    $conn = getDBConnection();
    
    $razorpay_payment_id = $payment_entity['id'];
    $razorpay_order_id = $payment_entity['order_id'];
    
    // Update payment status
    $stmt = $conn->prepare("
        UPDATE payments 
        SET status = 'failed', 
            razorpay_payment_id = ?,
            updated_at = NOW()
        WHERE razorpay_order_id = ?
    ");
    
    $stmt->execute([$razorpay_payment_id, $razorpay_order_id]);
}

/**
 * Generate token for successful payment
 */
function generateTokenForPayment($payment_id) {
    $conn = getDBConnection();
    
    // Check if token already exists
    $stmt = $conn->prepare("SELECT token_id FROM tokens WHERE payment_id = ?");
    $stmt->execute([$payment_id]);
    $existing_token = $stmt->fetch();
    
    if (!$existing_token) {
        // Get payment details
        $stmt = $conn->prepare("
            SELECT p.*, u.name 
            FROM payments p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.payment_id = ?
        ");
        $stmt->execute([$payment_id]);
        $payment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($payment) {
            // Generate unique token
            $token_id = generateToken();
            
            // Insert token
            $stmt = $conn->prepare("
                INSERT INTO tokens (token_id, user_id, payment_id, amount, status) 
                VALUES (?, ?, ?, ?, 'Pending')
            ");
            $stmt->execute([
                $token_id, 
                $payment['user_id'], 
                $payment_id, 
                $payment['amount']
            ]);
            
            // Update payment with token ID
            $stmt = $conn->prepare("
                UPDATE payments 
                SET token_id = ?, updated_at = NOW() 
                WHERE payment_id = ?
            ");
            $stmt->execute([$token_id, $payment_id]);
            
            logActivity("Token generated: $token_id for user: " . $payment['name'], 'system', 0);
        }
    }
}

/**
 * Get payment details from Razorpay
 */
function getRazorpayPayment($payment_id) {
    $url = "https://api.razorpay.com/v1/payments/$payment_id";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}
?>
