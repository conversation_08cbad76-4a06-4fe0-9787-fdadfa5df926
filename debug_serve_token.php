<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// Start session to simulate admin/server login
session_start();

echo "<h1>Debug: Serve Token Functionality</h1>";

// Test database connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $conn = getDBConnection();
    echo "✅ Database connection successful<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

// Check table structure
echo "<h2>2. Table Structure Check</h2>";
try {
    $stmt = $conn->prepare("DESCRIBE tokens");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if served_by and served_at columns exist
    $has_served_by = false;
    $has_served_at = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'served_by') $has_served_by = true;
        if ($column['Field'] === 'served_at') $has_served_at = true;
    }
    
    if ($has_served_by && $has_served_at) {
        echo "✅ Both served_by and served_at columns exist<br>";
    } else {
        echo "❌ Missing columns: ";
        if (!$has_served_by) echo "served_by ";
        if (!$has_served_at) echo "served_at ";
        echo "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking table structure: " . $e->getMessage() . "<br>";
}

// Check server_users table
echo "<h2>3. Server Users Check</h2>";
try {
    $stmt = $conn->prepare("SELECT id, username, full_name FROM server_users");
    $stmt->execute();
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($servers)) {
        echo "❌ No server users found<br>";
    } else {
        echo "✅ Server users found:<br>";
        foreach ($servers as $server) {
            echo "- ID: {$server['id']}, Username: {$server['username']}, Name: {$server['full_name']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking server users: " . $e->getMessage() . "<br>";
}

// Create a test token if none exists
echo "<h2>4. Test Token Creation</h2>";
try {
    // Check if we have any pending tokens
    $stmt = $conn->prepare("SELECT token_id FROM tokens WHERE status = 'Pending' LIMIT 1");
    $stmt->execute();
    $existing_token = $stmt->fetch();
    
    if (!$existing_token) {
        // Create a test user first
        $stmt = $conn->prepare("INSERT IGNORE INTO users (name, uid, phone, email) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Test User', 'TEST123', '9876543210', '<EMAIL>']);
        $user_id = $conn->lastInsertId();
        
        if (!$user_id) {
            // Get existing user
            $stmt = $conn->prepare("SELECT id FROM users WHERE uid = 'TEST123'");
            $stmt->execute();
            $user = $stmt->fetch();
            $user_id = $user['id'];
        }
        
        // Create test token
        $test_token_id = 'MESS-TEST-' . date('Ymd-His');
        $stmt = $conn->prepare("INSERT INTO tokens (token_id, user_id, amount, status) VALUES (?, ?, ?, 'Pending')");
        $stmt->execute([$test_token_id, $user_id, 70.00]);
        
        echo "✅ Created test token: $test_token_id<br>";
        $test_token = $test_token_id;
    } else {
        $test_token = $existing_token['token_id'];
        echo "✅ Using existing token: $test_token<br>";
    }
} catch (Exception $e) {
    echo "❌ Error creating test token: " . $e->getMessage() . "<br>";
    $test_token = null;
}

// Test the serve token functionality
if ($test_token) {
    echo "<h2>5. Test Serve Token Functionality</h2>";
    
    // Simulate server login
    $_SESSION['server_id'] = 1; // Assuming server ID 1 exists
    
    echo "<h3>5.1 Test getTokenByTokenId function</h3>";
    try {
        $token_data = getTokenByTokenId($test_token);
        if ($token_data) {
            echo "✅ Token found: " . json_encode($token_data) . "<br>";
        } else {
            echo "❌ Token not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error getting token: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>5.2 Test updateTokenStatus function</h3>";
    try {
        $success = updateTokenStatus($test_token, 'Served', 1);
        if ($success) {
            echo "✅ Token status updated successfully<br>";
            
            // Verify the update
            $stmt = $conn->prepare("SELECT status, served_at, served_by FROM tokens WHERE token_id = ?");
            $stmt->execute([$test_token]);
            $updated_token = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "Updated token data: " . json_encode($updated_token) . "<br>";
        } else {
            echo "❌ Failed to update token status<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error updating token status: " . $e->getMessage() . "<br>";
        echo "SQL Error Info: " . print_r($conn->errorInfo(), true) . "<br>";
    }
    
    echo "<h3>5.3 Test Complete API Call</h3>";
    try {
        // Reset token to pending first
        $stmt = $conn->prepare("UPDATE tokens SET status = 'Pending', served_at = NULL, served_by = NULL WHERE token_id = ?");
        $stmt->execute([$test_token]);
        
        // Test the complete API flow
        $input = ['token_id' => $test_token];
        
        // Check authentication
        if (!isServer() && !isAdmin()) {
            echo "❌ Authentication check failed<br>";
        } else {
            echo "✅ Authentication check passed<br>";
        }
        
        // Get token details
        $token = getTokenByTokenId($test_token);
        if (!$token) {
            echo "❌ Token not found in API test<br>";
        } else {
            echo "✅ Token found in API test<br>";
        }
        
        if ($token['status'] !== 'Pending') {
            echo "❌ Token status is not Pending: " . $token['status'] . "<br>";
        } else {
            echo "✅ Token status is Pending<br>";
        }
        
        // Mark token as served
        $served_by = isServer() ? $_SESSION['server_id'] : null;
        $success = updateTokenStatus($test_token, 'Served', $served_by);
        
        if ($success) {
            echo "✅ Complete API test successful<br>";
        } else {
            echo "❌ Complete API test failed<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error in complete API test: " . $e->getMessage() . "<br>";
    }
}

// Check foreign key constraints
echo "<h2>6. Foreign Key Constraints Check</h2>";
try {
    $stmt = $conn->prepare("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = 'mess_management' 
            AND TABLE_NAME = 'tokens'
    ");
    $stmt->execute();
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($constraints)) {
        echo "ℹ️ No foreign key constraints found<br>";
    } else {
        echo "Foreign key constraints:<br>";
        foreach ($constraints as $constraint) {
            echo "- {$constraint['CONSTRAINT_NAME']}: {$constraint['COLUMN_NAME']} -> {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking foreign keys: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p><a href='index.php'>Go to Home Page</a></p>";
?>
