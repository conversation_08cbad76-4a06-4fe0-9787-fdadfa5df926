<?php
$page_title = 'Search Token';
$show_nav = true;
require_once '../includes/header.php';

// Check server authentication
if (!isServer()) {
    $_SESSION['error_message'] = 'Please login as server staff to access this page.';
    redirect('../auth/server_login.php');
}

$search_results = [];
$search_performed = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $search_type = $_POST['search_type'] ?? '';
    $search_value = sanitizeInput($_POST['search_value']);
    
    if (!empty($search_value)) {
        try {
            $conn = getDBConnection();
            $search_performed = true;
            
            switch ($search_type) {
                case 'token_id':
                    $stmt = $conn->prepare("
                        SELECT t.*, u.name, u.uid, u.phone 
                        FROM tokens t 
                        JOIN users u ON t.user_id = u.id 
                        WHERE t.token_id LIKE ?
                    ");
                    $stmt->execute(["%$search_value%"]);
                    break;
                    
                case 'phone':
                    $stmt = $conn->prepare("
                        SELECT t.*, u.name, u.uid, u.phone 
                        FROM tokens t 
                        JOIN users u ON t.user_id = u.id 
                        WHERE u.phone LIKE ?
                    ");
                    $stmt->execute(["%$search_value%"]);
                    break;
                    
                case 'uid':
                    $stmt = $conn->prepare("
                        SELECT t.*, u.name, u.uid, u.phone 
                        FROM tokens t 
                        JOIN users u ON t.user_id = u.id 
                        WHERE u.uid LIKE ?
                    ");
                    $stmt->execute(["%$search_value%"]);
                    break;
                    
                case 'name':
                    $stmt = $conn->prepare("
                        SELECT t.*, u.name, u.uid, u.phone 
                        FROM tokens t 
                        JOIN users u ON t.user_id = u.id 
                        WHERE u.name LIKE ?
                    ");
                    $stmt->execute(["%$search_value%"]);
                    break;
                    
                default:
                    // Search all fields
                    $stmt = $conn->prepare("
                        SELECT t.*, u.name, u.uid, u.phone 
                        FROM tokens t 
                        JOIN users u ON t.user_id = u.id 
                        WHERE t.token_id LIKE ? OR u.name LIKE ? OR u.uid LIKE ? OR u.phone LIKE ?
                    ");
                    $stmt->execute(["%$search_value%", "%$search_value%", "%$search_value%", "%$search_value%"]);
            }
            
            $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'Search failed. Please try again.';
            logActivity("Server search error: " . $e->getMessage());
        }
    }
}
?>

<div class="card">
    <div class="card-header">
        <h3>Search Tokens</h3>
    </div>
    <div class="card-body">
        <form method="POST" data-validate="true">
            <div class="form-group">
                <label for="search_type" class="form-label">Search By</label>
                <select id="search_type" name="search_type" class="form-control">
                    <option value="">All Fields</option>
                    <option value="token_id" <?php echo ($_POST['search_type'] ?? '') === 'token_id' ? 'selected' : ''; ?>>Token ID</option>
                    <option value="phone" <?php echo ($_POST['search_type'] ?? '') === 'phone' ? 'selected' : ''; ?>>Phone Number</option>
                    <option value="uid" <?php echo ($_POST['search_type'] ?? '') === 'uid' ? 'selected' : ''; ?>>UID</option>
                    <option value="name" <?php echo ($_POST['search_type'] ?? '') === 'name' ? 'selected' : ''; ?>>Customer Name</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="search_value" class="form-label">Search Value</label>
                <input 
                    type="text" 
                    id="search_value" 
                    name="search_value" 
                    class="form-control" 
                    required 
                    value="<?php echo isset($_POST['search_value']) ? htmlspecialchars($_POST['search_value']) : ''; ?>"
                    placeholder="Enter search term..."
                >
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Search</button>
                <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
            </div>
        </form>
    </div>
</div>

<?php if ($search_performed): ?>
<div class="card">
    <div class="card-header">
        <h3>Search Results</h3>
    </div>
    <div class="card-body">
        <?php if (empty($search_results)): ?>
            <div class="alert alert-warning">
                No tokens found matching your search criteria.
            </div>
        <?php else: ?>
            <div style="margin-bottom: 1rem;">
                <strong>Found <?php echo count($search_results); ?> token(s)</strong>
            </div>
            
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Token ID</th>
                            <th>Customer</th>
                            <th>UID</th>
                            <th>Phone</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($search_results as $token): ?>
                            <tr>
                                <td>
                                    <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($token['token_id']); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($token['name']); ?></td>
                                <td><?php echo htmlspecialchars($token['uid']); ?></td>
                                <td><?php echo htmlspecialchars($token['phone']); ?></td>
                                <td><?php echo formatCurrency($token['amount']); ?></td>
                                <td><?php echo getStatusBadge($token['status']); ?></td>
                                <td><?php echo date('M j, Y H:i', strtotime($token['created_at'])); ?></td>
                                <td>
                                    <?php if ($token['status'] === 'Pending'): ?>
                                        <button class="btn btn-success btn-sm serve-token-btn" 
                                                data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                            Serve
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn btn-secondary btn-sm copy-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Copy
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h3>Quick Search Tips</h3>
    </div>
    <div class="card-body">
        <ul style="margin: 0; padding-left: 1.5rem;">
            <li><strong>Token ID:</strong> Search by full or partial token ID (e.g., "MESS-ABC123")</li>
            <li><strong>Phone Number:</strong> Search by phone number (e.g., "9876543210")</li>
            <li><strong>UID:</strong> Search by student/employee UID</li>
            <li><strong>Customer Name:</strong> Search by customer name</li>
            <li><strong>All Fields:</strong> Search across all fields simultaneously</li>
        </ul>
        
        <div style="margin-top: 1rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; border-left: 4px solid #007bff;">
            <strong>Pro Tip:</strong> You can use partial matches. For example, searching "9876" will find all phone numbers containing those digits.
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
