<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>Payment Integration Test</h1>";

// Test configuration
echo "<h2>Configuration Test</h2>";
echo "Razorpay Key ID: " . (defined('RAZORPAY_KEY_ID') ? RAZOR<PERSON>Y_KEY_ID : 'Not defined') . "<br>";
echo "Razorpay Key Secret: " . (defined('RAZORPAY_KEY_SECRET') ? substr(RAZORPAY_KEY_SECRET, 0, 8) . '...' : 'Not defined') . "<br>";
echo "Payment Mode: " . (defined('PAYMENT_MODE') ? PAYMENT_MODE : 'Not defined') . "<br>";
echo "Site URL: " . (defined('SITE_URL') ? SITE_URL : 'Not defined') . "<br>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    $conn = getDBConnection();
    echo "✅ Database connection successful<br>";
    
    // Test payments table
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM payments");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ Payments table accessible. Total payments: " . $result['count'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test Razorpay API connectivity
echo "<h2>Razorpay API Test</h2>";
if (PAYMENT_MODE === 'live') {
    $test_order_data = [
        'amount' => 100, // 1 rupee in paise
        'currency' => 'INR',
        'receipt' => 'test_' . time()
    ];
    
    $url = 'https://api.razorpay.com/v1/orders';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_order_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        echo "✅ Razorpay API connection successful<br>";
        $order = json_decode($response, true);
        echo "Test order created: " . $order['id'] . "<br>";
    } else {
        echo "❌ Razorpay API error (HTTP $http_code): $response<br>";
    }
} else {
    echo "ℹ️ Demo mode active - Razorpay API test skipped<br>";
}

// Test signature verification function
echo "<h2>Signature Verification Test</h2>";
$test_order_id = 'order_test123';
$test_payment_id = 'pay_test123';
$test_signature = hash_hmac('sha256', $test_order_id . "|" . $test_payment_id, RAZORPAY_KEY_SECRET);

if (verifyRazorpaySignature($test_order_id, $test_payment_id, $test_signature)) {
    echo "✅ Signature verification function working correctly<br>";
} else {
    echo "❌ Signature verification function failed<br>";
}

echo "<h2>File Structure Test</h2>";
$required_files = [
    'api/create_order.php',
    'api/payment_callback.php',
    'user/payment.php',
    'user/payment_success.php',
    'user/payment_failed.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='index.php'>Go to Home Page</a></p>";
?>
