<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

// Log the logout activity
if (isAdmin()) {
    logActivity("Admin logout", 'admin', $_SESSION['admin_id']);
} elseif (isServer()) {
    logActivity("Server logout", 'server', $_SESSION['server_id']);
}

// Clear all session data
session_destroy();

// Start a new session for the success message
session_start();
$_SESSION['success_message'] = 'You have been logged out successfully.';

// Redirect to home page
redirect('../index.php');
?>
