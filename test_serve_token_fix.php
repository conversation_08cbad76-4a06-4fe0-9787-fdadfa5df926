<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// Start session and simulate server login
session_start();

echo "<h1>Test: Serve <PERSON>ken Fix</h1>";

try {
    $conn = getDBConnection();
    
    // First, run the migration to ensure columns exist
    echo "<h2>Step 1: Ensure Database Schema is Correct</h2>";
    
    // Check if served_by and served_at columns exist
    $stmt = $conn->prepare("DESCRIBE tokens");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existing_columns = array_column($columns, 'Field');
    $needs_migration = false;
    
    if (!in_array('served_by', $existing_columns)) {
        echo "Adding served_by column...<br>";
        $conn->exec("ALTER TABLE tokens ADD COLUMN served_by INT NULL");
        $needs_migration = true;
    }
    
    if (!in_array('served_at', $existing_columns)) {
        echo "Adding served_at column...<br>";
        $conn->exec("ALTER TABLE tokens ADD COLUMN served_at TIMESTAMP NULL");
        $needs_migration = true;
    }
    
    if ($needs_migration) {
        // Add foreign key constraint
        try {
            $conn->exec("ALTER TABLE tokens ADD FOREIGN KEY (served_by) REFERENCES server_users(id) ON DELETE SET NULL");
            echo "Added foreign key constraint.<br>";
        } catch (Exception $e) {
            echo "Foreign key constraint may already exist: " . $e->getMessage() . "<br>";
        }
        echo "✅ Database schema updated<br>";
    } else {
        echo "✅ Database schema is already correct<br>";
    }
    
    // Step 2: Ensure we have a server user
    echo "<h2>Step 2: Ensure Server User Exists</h2>";
    $stmt = $conn->prepare("SELECT id FROM server_users LIMIT 1");
    $stmt->execute();
    $server = $stmt->fetch();
    
    if (!$server) {
        // Create a test server user
        $password = password_hash('server123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO server_users (username, password, full_name) VALUES (?, ?, ?)");
        $stmt->execute(['testserver', $password, 'Test Server User']);
        $server_id = $conn->lastInsertId();
        echo "✅ Created test server user with ID: $server_id<br>";
    } else {
        $server_id = $server['id'];
        echo "✅ Server user exists with ID: $server_id<br>";
    }
    
    // Step 3: Create a test token
    echo "<h2>Step 3: Create Test Token</h2>";
    
    // Ensure we have a test user
    $stmt = $conn->prepare("SELECT id FROM users WHERE uid = 'TESTFIX123'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if (!$user) {
        $stmt = $conn->prepare("INSERT INTO users (name, uid, phone, email) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Test Fix User', 'TESTFIX123', '9876543210', '<EMAIL>']);
        $user_id = $conn->lastInsertId();
        echo "✅ Created test user with ID: $user_id<br>";
    } else {
        $user_id = $user['id'];
        echo "✅ Test user exists with ID: $user_id<br>";
    }
    
    // Create test token
    $test_token_id = 'MESS-TESTFIX-' . date('Ymd-His');
    $stmt = $conn->prepare("INSERT INTO tokens (token_id, user_id, amount, status) VALUES (?, ?, ?, 'Pending')");
    $stmt->execute([$test_token_id, $user_id, 70.00]);
    echo "✅ Created test token: $test_token_id<br>";
    
    // Step 4: Simulate server login and test serve token
    echo "<h2>Step 4: Test Serve Token Functionality</h2>";
    $_SESSION['server_id'] = $server_id;
    
    echo "Simulating server login with ID: $server_id<br>";
    
    // Test the serve token function
    echo "Testing updateTokenStatus function...<br>";
    $success = updateTokenStatus($test_token_id, 'Served', $server_id);
    
    if ($success) {
        echo "✅ updateTokenStatus function worked successfully<br>";
        
        // Verify the update
        $stmt = $conn->prepare("SELECT status, served_at, served_by FROM tokens WHERE token_id = ?");
        $stmt->execute([$test_token_id]);
        $updated_token = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "Updated token data:<br>";
        echo "- Status: " . $updated_token['status'] . "<br>";
        echo "- Served At: " . $updated_token['served_at'] . "<br>";
        echo "- Served By: " . $updated_token['served_by'] . "<br>";
        
        // Step 5: Test the complete API
        echo "<h2>Step 5: Test Complete API</h2>";
        
        // Reset token to pending
        $stmt = $conn->prepare("UPDATE tokens SET status = 'Pending', served_at = NULL, served_by = NULL WHERE token_id = ?");
        $stmt->execute([$test_token_id]);
        
        // Test the API function
        $input = ['token_id' => $test_token_id];
        
        ob_start();
        try {
            handleServeToken($conn, $input);
        } catch (Exception $e) {
            echo "API Error: " . $e->getMessage() . "<br>";
        }
        $api_output = ob_get_clean();
        
        if (empty($api_output)) {
            echo "❌ API function didn't produce output (this might be normal if it uses sendJsonResponse)<br>";
        } else {
            echo "API Output: " . $api_output . "<br>";
        }
        
        // Check if token was actually updated
        $stmt = $conn->prepare("SELECT status, served_at, served_by FROM tokens WHERE token_id = ?");
        $stmt->execute([$test_token_id]);
        $final_token = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($final_token['status'] === 'Served') {
            echo "✅ Complete API test successful!<br>";
            echo "Final token status: " . $final_token['status'] . "<br>";
        } else {
            echo "❌ API test failed. Token status: " . $final_token['status'] . "<br>";
        }
        
    } else {
        echo "❌ updateTokenStatus function failed<br>";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;'>";
    echo "<h3>✅ Test Complete</h3>";
    echo "<p>The serve token functionality should now work properly. The main issues that were fixed:</p>";
    echo "<ul>";
    echo "<li>Ensured served_by and served_at columns exist in the tokens table</li>";
    echo "<li>Added proper validation for server_id in the updateTokenStatus function</li>";
    echo "<li>Added better error handling and logging</li>";
    echo "<li>Added foreign key constraint validation</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<p><a href='admin/dashboard.php'>Test Admin Dashboard</a> | <a href='server/dashboard.php'>Test Server Dashboard</a> | <a href='index.php'>Go to Home</a></p>";

// Include the handleServeToken function for testing
function handleServeToken($conn, $input) {
    try {
        // Check authentication
        if (!isServer() && !isAdmin()) {
            echo "Authentication failed<br>";
            return false;
        }
        
        $token_id = $input['token_id'] ?? '';
        
        if (empty($token_id)) {
            echo "Token ID is required<br>";
            return false;
        }
        
        // Get token details
        $token = getTokenByTokenId($token_id);
        
        if (!$token) {
            echo "Token not found<br>";
            return false;
        }
        
        if ($token['status'] !== 'Pending') {
            echo "Token has already been processed. Current status: " . $token['status'] . "<br>";
            return false;
        }
        
        // Mark token as served
        $served_by = isServer() ? $_SESSION['server_id'] : null;
        
        // Validate server_id exists if provided
        if ($served_by !== null) {
            $stmt = $conn->prepare("SELECT id FROM server_users WHERE id = ?");
            $stmt->execute([$served_by]);
            if (!$stmt->fetch()) {
                echo "Invalid server_id: $served_by<br>";
                $served_by = null;
            }
        }
        
        $success = updateTokenStatus($token_id, 'Served', $served_by);
        
        if ($success) {
            $user_type = isServer() ? 'server' : 'admin';
            $user_id = isServer() ? $_SESSION['server_id'] : $_SESSION['admin_id'];
            logActivity("Token served: $token_id for user: " . $token['name'], $user_type, $user_id);
            
            echo "Token marked as served successfully<br>";
            return true;
        } else {
            echo "Failed to update token status<br>";
            return false;
        }
        
    } catch (Exception $e) {
        echo "Error in handleServeToken: " . $e->getMessage() . "<br>";
        return false;
    }
}
?>
