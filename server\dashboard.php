<?php
$page_title = 'Server Dashboard';
$show_nav = true;
require_once '../includes/header.php';

// Check server authentication
if (!isServer()) {
    $_SESSION['error_message'] = 'Please login as server staff to access this page.';
    redirect('../auth/server_login.php');
}

try {
    $conn = getDBConnection();
    
    // Get today's statistics
    $stats = [];
    
    // Pending tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Pending'
    ");
    $stmt->execute();
    $stats['pending_tokens'] = $stmt->fetch()['count'];
    
    // Served tokens today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(created_at) = CURDATE() AND status = 'Served'
    ");
    $stmt->execute();
    $stats['served_tokens'] = $stmt->fetch()['count'];
    
    // Tokens served by this server today
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM tokens 
        WHERE DATE(served_at) = CURDATE() AND served_by = ?
    ");
    $stmt->execute([$_SESSION['server_id']]);
    $stats['my_served_tokens'] = $stmt->fetch()['count'];
    
    // Recent pending tokens
    $stmt = $conn->prepare("
        SELECT t.*, u.name, u.uid, u.phone 
        FROM tokens t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'Pending'
        ORDER BY t.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $pending_tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading dashboard data.';
    logActivity("Server dashboard error: " . $e->getMessage());
    $stats = ['pending_tokens' => 0, 'served_tokens' => 0, 'my_served_tokens' => 0];
    $pending_tokens = [];
}
?>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number" style="color: #ffc107;"><?php echo $stats['pending_tokens']; ?></div>
        <div class="stat-label">Pending Tokens</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number" style="color: #28a745;"><?php echo $stats['served_tokens']; ?></div>
        <div class="stat-label">Total Served Today</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number" style="color: #17a2b8;"><?php echo $stats['my_served_tokens']; ?></div>
        <div class="stat-label">Served by Me</div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Search Token</h3>
    </div>
    <div class="card-body">
        <form id="search-token-form">
            <div class="form-group">
                <label for="token-search" class="form-label">Enter Token ID</label>
                <div style="display: flex; gap: 1rem;">
                    <input 
                        type="text" 
                        id="token-search" 
                        class="form-control" 
                        placeholder="Enter token ID (e.g., MESS-ABC123-20250730)"
                        data-validate-type="token"
                        style="flex: 1;"
                    >
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </div>
        </form>
        
        <div id="token-result" style="margin-top: 2rem;"></div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="search_token.php" class="btn btn-secondary">Advanced Search</a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Pending Tokens</h3>
    </div>
    <div class="card-body">
        <?php if (empty($pending_tokens)): ?>
            <p style="text-align: center; color: #666; padding: 2rem;">No pending tokens found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Token ID</th>
                            <th>Customer</th>
                            <th>UID</th>
                            <th>Phone</th>
                            <th>Amount</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_tokens as $token): ?>
                            <tr>
                                <td>
                                    <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($token['token_id']); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($token['name']); ?></td>
                                <td><?php echo htmlspecialchars($token['uid']); ?></td>
                                <td><?php echo htmlspecialchars($token['phone']); ?></td>
                                <td><?php echo formatCurrency($token['amount']); ?></td>
                                <td><?php echo date('H:i', strtotime($token['created_at'])); ?></td>
                                <td>
                                    <button class="btn btn-success btn-sm serve-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Serve
                                    </button>
                                    <button class="btn btn-secondary btn-sm copy-token-btn" 
                                            data-token-id="<?php echo htmlspecialchars($token['token_id']); ?>">
                                        Copy
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Token search functionality
document.getElementById('search-token-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const tokenId = document.getElementById('token-search').value.trim();
    const resultDiv = document.getElementById('token-result');
    
    if (!tokenId) {
        resultDiv.innerHTML = '<div class="alert alert-warning">Please enter a token ID.</div>';
        return;
    }
    
    // Show loading
    resultDiv.innerHTML = '<div style="text-align: center;"><span class="loading"></span> Searching...</div>';
    
    // Search for token
    fetch('../api/token_operations.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'search_token',
            token_id: tokenId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.token) {
            const token = data.token;
            resultDiv.innerHTML = `
                <div class="card" style="border: 2px solid ${token.status === 'Pending' ? '#ffc107' : token.status === 'Served' ? '#28a745' : '#dc3545'};">
                    <div class="card-header" style="background: ${token.status === 'Pending' ? '#fff3cd' : token.status === 'Served' ? '#d4edda' : '#f8d7da'};">
                        <h4 style="margin: 0;">Token Found</h4>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div><strong>Token ID:</strong> ${token.token_id}</div>
                            <div><strong>Status:</strong> ${getStatusBadge(token.status)}</div>
                            <div><strong>Customer:</strong> ${token.name}</div>
                            <div><strong>UID:</strong> ${token.uid}</div>
                            <div><strong>Phone:</strong> ${token.phone}</div>
                            <div><strong>Amount:</strong> ₹${token.amount}</div>
                            <div><strong>Created:</strong> ${new Date(token.created_at).toLocaleString()}</div>
                            ${token.served_at ? `<div><strong>Served:</strong> ${new Date(token.served_at).toLocaleString()}</div>` : ''}
                        </div>
                        ${token.status === 'Pending' ? 
                            `<button class="btn btn-success serve-token-btn" data-token-id="${token.token_id}">Mark as Served</button>` : 
                            token.status === 'Served' ? 
                                '<div class="alert alert-success">This token has already been served.</div>' :
                                '<div class="alert alert-danger">This token has expired.</div>'
                        }
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">Token not found. Please check the token ID and try again.</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger">An error occurred while searching. Please try again.</div>';
        console.error('Error:', error);
    });
});

function getStatusBadge(status) {
    switch(status) {
        case 'Pending':
            return '<span class="badge badge-warning">Pending</span>';
        case 'Served':
            return '<span class="badge badge-success">Served</span>';
        case 'Expired':
            return '<span class="badge badge-danger">Expired</span>';
        default:
            return '<span class="badge badge-secondary">Unknown</span>';
    }
}

// Auto-refresh pending tokens every 15 seconds
setInterval(function() {
    location.reload();
}, 15000);
</script>

<?php require_once '../includes/footer.php'; ?>
