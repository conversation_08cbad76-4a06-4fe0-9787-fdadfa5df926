// Mess Management System JavaScript

// Global variables
let isProcessing = false;

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize form validation
    initFormValidation();

    // Initialize search functionality
    initSearch();

    // Initialize auto-refresh for dashboards
    initAutoRefresh();

    // Initialize tooltips and other UI elements
    initUI();
});

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate="true"]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });

        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    const type = field.getAttribute('data-validate-type');
    let isValid = true;
    let message = '';

    // Remove existing error
    clearFieldError(field);

    // Required field check
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'This field is required';
    }

    // Type-specific validation
    if (value && type) {
        switch (type) {
            case 'phone':
                if (!/^[6-9]\d{9}$/.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid 10-digit phone number';
                }
                break;

            case 'uid':
                if (!/^[A-Za-z0-9]{6,20}$/.test(value)) {
                    isValid = false;
                    message = 'UID must be 6-20 characters (letters and numbers only)';
                }
                break;

            case 'email':
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid email address';
                }
                break;

            case 'token':
                if (!/^MESS-[A-Z0-9]+-\d{8}$/.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid token ID';
                }
                break;
        }
    }

    if (!isValid) {
        showFieldError(field, message);
    }

    return isValid;
}

// Validate entire form
function validateForm(form) {
    const fields = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    fields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    return isValid;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('error');

    let errorDiv = field.parentNode.querySelector('.error-message');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        field.parentNode.appendChild(errorDiv);
    }

    errorDiv.textContent = message;
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('error');
    const errorDiv = field.parentNode.querySelector('.error-message');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Search functionality
function initSearch() {
    const searchInputs = document.querySelectorAll('.search-input');

    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const targetTable = document.querySelector(this.getAttribute('data-target'));

            if (targetTable) {
                filterTable(targetTable, searchTerm);
            }
        });
    });
}

// Filter table rows
function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Auto-refresh for dashboards
function initAutoRefresh() {
    const refreshElements = document.querySelectorAll('[data-auto-refresh]');

    refreshElements.forEach(element => {
        const interval = parseInt(element.getAttribute('data-auto-refresh')) * 1000;
        const url = element.getAttribute('data-refresh-url');

        if (url && interval) {
            setInterval(() => {
                refreshContent(element, url);
            }, interval);
        }
    });
}

// Refresh content via AJAX
function refreshContent(element, url) {
    fetch(url)
        .then(response => response.text())
        .then(html => {
            element.innerHTML = html;
        })
        .catch(error => {
            console.error('Refresh failed:', error);
        });
}

// Get correct API path based on current location
function getApiPath(endpoint) {
    // Get current path to determine directory level
    const currentPath = window.location.pathname;

    // If we're in a subdirectory (admin/, server/, user/, auth/), go up one level
    if (currentPath.includes('/admin/') ||
        currentPath.includes('/server/') ||
        currentPath.includes('/user/') ||
        currentPath.includes('/auth/')) {
        return '../api/' + endpoint;
    }

    // If we're at root level, use relative path
    return 'api/' + endpoint;
}

// Initialize UI elements
function initUI() {
    // Add click handlers for action buttons
    document.addEventListener('click', function(e) {
        // Serve token button
        if (e.target.classList.contains('serve-token-btn')) {
            e.preventDefault();
            serveToken(e.target.getAttribute('data-token-id'));
        }

        // Delete token button
        if (e.target.classList.contains('delete-token-btn')) {
            e.preventDefault();
            if (confirm('Are you sure you want to delete this token?')) {
                deleteToken(e.target.getAttribute('data-token-id'));
            }
        }

        // Copy token button
        if (e.target.classList.contains('copy-token-btn')) {
            e.preventDefault();
            copyToClipboard(e.target.getAttribute('data-token-id'));
        }
    });
}

// Serve token
function serveToken(tokenId) {
    if (isProcessing) return;

    isProcessing = true;
    showLoading('Marking token as served...');

    // Get the correct API path based on current location
    const apiPath = getApiPath('token_operations.php');

    fetch(apiPath, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'serve_token',
            token_id: tokenId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showAlert('Token marked as served successfully!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Failed to serve token', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('An error occurred. Please try again.', 'danger');
        console.error('Error:', error);
    })
    .finally(() => {
        isProcessing = false;
    });
}

// Delete token
function deleteToken(tokenId) {
    if (isProcessing) return;

    isProcessing = true;
    showLoading('Deleting token...');

    // Get the correct API path based on current location
    const apiPath = getApiPath('token_operations.php');

    fetch(apiPath, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'delete_token',
            token_id: tokenId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showAlert('Token deleted successfully!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Failed to delete token', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('An error occurred. Please try again.', 'danger');
        console.error('Error:', error);
    })
    .finally(() => {
        isProcessing = false;
    });
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('Token ID copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('Token ID copied to clipboard!', 'success');
    });
}

// Show loading overlay
function showLoading(message = 'Processing...') {
    let overlay = document.getElementById('loading-overlay');

    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 1.2rem;
        `;
        document.body.appendChild(overlay);
    }

    overlay.innerHTML = `
        <div style="text-align: center;">
            <div class="loading" style="margin: 0 auto 1rem;"></div>
            <div>${message}</div>
        </div>
    `;
    overlay.style.display = 'flex';
}

// Hide loading overlay
function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    alertDiv.textContent = message;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Payment processing
function processPayment(formData) {
    showLoading('Processing payment...');

    // This will be handled by Razorpay integration
    // The actual payment processing will be done server-side
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.MessSystem = {
    validateForm,
    validateField,
    showAlert,
    showLoading,
    hideLoading,
    copyToClipboard,
    serveToken,
    deleteToken,
    formatCurrency,
    getApiPath
};
