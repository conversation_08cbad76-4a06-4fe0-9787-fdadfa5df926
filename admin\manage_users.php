<?php
$page_title = 'Manage Users';
$show_nav = true;
require_once '../includes/header.php';

// Check admin authentication
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Please login as admin to access this page.';
    redirect('../auth/admin_login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $conn = getDBConnection();
        
        if ($action === 'add_admin') {
            $username = sanitizeInput($_POST['username']);
            $password = $_POST['password'];
            $email = sanitizeInput($_POST['email']);
            
            if (empty($username) || empty($password) || empty($email)) {
                throw new Exception('All fields are required.');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('Password must be at least 6 characters long.');
            }
            
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
            $stmt->execute([$username, $hashed_password, $email]);
            
            $_SESSION['success_message'] = 'Admin user created successfully.';
            logActivity("New admin user created: $username", 'admin', $_SESSION['admin_id']);
            
        } elseif ($action === 'add_server') {
            $username = sanitizeInput($_POST['username']);
            $password = $_POST['password'];
            $full_name = sanitizeInput($_POST['full_name']);
            
            if (empty($username) || empty($password) || empty($full_name)) {
                throw new Exception('All fields are required.');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('Password must be at least 6 characters long.');
            }
            
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("INSERT INTO server_users (username, password, full_name) VALUES (?, ?, ?)");
            $stmt->execute([$username, $hashed_password, $full_name]);
            
            $_SESSION['success_message'] = 'Server user created successfully.';
            logActivity("New server user created: $username", 'admin', $_SESSION['admin_id']);
            
        } elseif ($action === 'delete_admin') {
            $user_id = $_POST['user_id'];
            
            // Prevent deleting current admin
            if ($user_id == $_SESSION['admin_id']) {
                throw new Exception('You cannot delete your own account.');
            }
            
            $stmt = $conn->prepare("DELETE FROM admin_users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $_SESSION['success_message'] = 'Admin user deleted successfully.';
            logActivity("Admin user deleted: ID $user_id", 'admin', $_SESSION['admin_id']);
            
        } elseif ($action === 'delete_server') {
            $user_id = $_POST['user_id'];
            
            $stmt = $conn->prepare("DELETE FROM server_users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $_SESSION['success_message'] = 'Server user deleted successfully.';
            logActivity("Server user deleted: ID $user_id", 'admin', $_SESSION['admin_id']);
            
        } elseif ($action === 'reset_password') {
            $user_type = $_POST['user_type'];
            $user_id = $_POST['user_id'];
            $new_password = $_POST['new_password'];
            
            if (strlen($new_password) < 6) {
                throw new Exception('Password must be at least 6 characters long.');
            }
            
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            if ($user_type === 'admin') {
                $stmt = $conn->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
            } else {
                $stmt = $conn->prepare("UPDATE server_users SET password = ? WHERE id = ?");
            }
            
            $stmt->execute([$hashed_password, $user_id]);
            
            $_SESSION['success_message'] = 'Password reset successfully.';
            logActivity("Password reset for $user_type user ID: $user_id", 'admin', $_SESSION['admin_id']);
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }
    
    // Redirect to prevent form resubmission
    redirect('manage_users.php');
}

try {
    $conn = getDBConnection();
    
    // Get all admin users
    $stmt = $conn->prepare("SELECT * FROM admin_users ORDER BY created_at DESC");
    $stmt->execute();
    $admin_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all server users
    $stmt = $conn->prepare("SELECT * FROM server_users ORDER BY created_at DESC");
    $stmt->execute();
    $server_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error loading user data.';
    $admin_users = [];
    $server_users = [];
}
?>

<style>
.user-management {
    max-width: 1200px;
    margin: 0 auto;
}

.user-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.user-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-card.current {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-warning:hover {
    background: #e0a800;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}
</style>

<div class="user-management">
    <div class="dashboard-header" style="text-align: center; margin-bottom: 2rem;">
        <h2>👥 User Management</h2>
        <p style="color: #666;">Manage admin and server user accounts</p>
    </div>

    <!-- Add New Users -->
    <div class="user-grid">
        <!-- Add Admin User -->
        <div class="card">
            <div class="card-header">
                <h3>🔐 Add New Admin</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="add_admin">
                    
                    <div class="form-group">
                        <label for="admin_username">Username:</label>
                        <input type="text" id="admin_username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_password">Password:</label>
                        <input type="password" id="admin_password" name="password" required minlength="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">Email:</label>
                        <input type="email" id="admin_email" name="email" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Add Admin</button>
                </form>
            </div>
        </div>

        <!-- Add Server User -->
        <div class="card">
            <div class="card-header">
                <h3>👨‍🍳 Add New Server Staff</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="add_server">
                    
                    <div class="form-group">
                        <label for="server_username">Username:</label>
                        <input type="text" id="server_username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="server_password">Password:</label>
                        <input type="password" id="server_password" name="password" required minlength="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="server_full_name">Full Name:</label>
                        <input type="text" id="server_full_name" name="full_name" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Add Server Staff</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Existing Users -->
    <div class="user-grid">
        <!-- Admin Users -->
        <div class="card">
            <div class="card-header">
                <h3>🔐 Admin Users (<?php echo count($admin_users); ?>)</h3>
            </div>
            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                <?php if (empty($admin_users)): ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No admin users found.</p>
                <?php else: ?>
                    <?php foreach ($admin_users as $user): ?>
                        <div class="user-card <?php echo $user['id'] == $_SESSION['admin_id'] ? 'current' : ''; ?>">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 0.5rem 0;">
                                        <?php echo htmlspecialchars($user['username']); ?>
                                        <?php if ($user['id'] == $_SESSION['admin_id']): ?>
                                            <span style="background: #007bff; color: white; padding: 0.2rem 0.4rem; border-radius: 3px; font-size: 0.7rem;">YOU</span>
                                        <?php endif; ?>
                                    </h4>
                                    <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                        <strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?><br>
                                        <strong>Created:</strong> <?php echo date('M j, Y', strtotime($user['created_at'])); ?><br>
                                        <strong>Last Login:</strong> <?php echo $user['last_login'] ? date('M j, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>
                                    </p>
                                </div>
                                <div class="btn-group">
                                    <button class="btn-small btn-warning" onclick="resetPassword('admin', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                        Reset Password
                                    </button>
                                    <?php if ($user['id'] != $_SESSION['admin_id']): ?>
                                        <button class="btn-small btn-danger" onclick="deleteUser('admin', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                            Delete
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Server Users -->
        <div class="card">
            <div class="card-header">
                <h3>👨‍🍳 Server Staff (<?php echo count($server_users); ?>)</h3>
            </div>
            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                <?php if (empty($server_users)): ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No server users found.</p>
                <?php else: ?>
                    <?php foreach ($server_users as $user): ?>
                        <div class="user-card">
                            <div style="display: flex; justify-content: between; align-items: center;">
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 0.5rem 0;"><?php echo htmlspecialchars($user['username']); ?></h4>
                                    <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                        <strong>Name:</strong> <?php echo htmlspecialchars($user['full_name']); ?><br>
                                        <strong>Created:</strong> <?php echo date('M j, Y', strtotime($user['created_at'])); ?><br>
                                        <strong>Last Login:</strong> <?php echo $user['last_login'] ? date('M j, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>
                                    </p>
                                </div>
                                <div class="btn-group">
                                    <button class="btn-small btn-warning" onclick="resetPassword('server', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                        Reset Password
                                    </button>
                                    <button class="btn-small btn-danger" onclick="deleteUser('server', <?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            <h3>⚡ Quick Actions</h3>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <a href="dashboard.php" class="btn btn-secondary">
                    📊 Back to Dashboard
                </a>
                <a href="view_tokens.php" class="btn btn-info">
                    🎫 View All Tokens
                </a>
                <a href="statistics.php" class="btn btn-success">
                    📈 View Statistics
                </a>
                <a href="../auth/logout.php" class="btn btn-danger">
                    🚪 Logout
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h3>🔑 Reset Password</h3>
        <form method="POST" id="resetPasswordForm">
            <input type="hidden" name="action" value="reset_password">
            <input type="hidden" name="user_type" id="reset_user_type">
            <input type="hidden" name="user_id" id="reset_user_id">
            
            <p>Reset password for: <strong id="reset_username"></strong></p>
            
            <div class="form-group">
                <label for="new_password">New Password:</label>
                <input type="password" id="new_password" name="new_password" required minlength="6">
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn btn-primary">Reset Password</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete User Modal -->
<div id="deleteUserModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h3>⚠️ Delete User</h3>
        <form method="POST" id="deleteUserForm">
            <input type="hidden" name="user_id" id="delete_user_id">
            <input type="hidden" name="action" id="delete_action">
            
            <p>Are you sure you want to delete user: <strong id="delete_username"></strong>?</p>
            <p style="color: #dc3545;"><strong>This action cannot be undone!</strong></p>
            
            <div class="btn-group">
                <button type="submit" class="btn btn-danger">Yes, Delete</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<script>
function resetPassword(userType, userId, username) {
    document.getElementById('reset_user_type').value = userType;
    document.getElementById('reset_user_id').value = userId;
    document.getElementById('reset_username').textContent = username;
    document.getElementById('new_password').value = '';
    document.getElementById('resetPasswordModal').style.display = 'block';
}

function deleteUser(userType, userId, username) {
    document.getElementById('delete_user_id').value = userId;
    document.getElementById('delete_action').value = 'delete_' + userType;
    document.getElementById('delete_username').textContent = username;
    document.getElementById('deleteUserModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('resetPasswordModal').style.display = 'none';
    document.getElementById('deleteUserModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const resetModal = document.getElementById('resetPasswordModal');
    const deleteModal = document.getElementById('deleteUserModal');
    
    if (event.target === resetModal) {
        resetModal.style.display = 'none';
    }
    if (event.target === deleteModal) {
        deleteModal.style.display = 'none';
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const passwords = form.querySelectorAll('input[type="password"]');
            passwords.forEach(password => {
                if (password.value.length < 6) {
                    e.preventDefault();
                    alert('Password must be at least 6 characters long');
                    password.focus();
                    return false;
                }
            });
        });
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
